<template>
  <BaseSingleSelectInput
    v-model="model"
    :options="options"
    emit-value
    map-options
    ref="inputRef"
    :input-style="{height: computedHeight}"
    :input-class="[$attrs['input-class'] ?? 'tw:text-l-design']"
    @filter="filterPartner"
    popup-content-class ="tw:max-w-full tw:tl:max-w-[40rem]"
  >
    <template v-slot:option="scope">
      <q-item v-bind="scope.props?.itemProps">
        <q-item-section>
          <q-item-label class="tw:text-l-design">{{
            scope.props?.opt?.label
          }}</q-item-label>
          <q-item-label caption class="tw:text-[#334155] tw:text-xs-design">{{
            scope.props?.opt?.description
          }}</q-item-label>
        </q-item-section>
      </q-item>
    </template>
  </BaseSingleSelectInput>
</template>

<script setup>
import { inject, ref, watch, onMounted } from 'vue';

import BaseSingleSelectInput from './BaseSingleSelectInput.vue';
const inputRef = ref(null);
const computedHeight = ref('16px');
const options = ref([]);
const listPartner = ref([]);

const model = defineModel();
const provideData = inject('basePartnerSelectProvideData');
const filterPartner = (val, update) => {
  if (val === '') {
    update(() => {
      options.value = listPartner.value;
    });
    return;
  }
  update(() => {
    const needle = val.toLowerCase();
    options.value = listPartner.value.filter(
      item => item.label.toLowerCase().indexOf(needle) > -1
    );
  });
};

watch(
  () => provideData.listPartner.value,
  val => {
    listPartner.value = val;
    options.value = val;
  }
);

onMounted(() => {
  listPartner.value = provideData.listPartner.value;
  options.value = provideData.listPartner.value;

  const el = inputRef.value?.$el?.querySelector('input');

  if (el) {
    const px = parseFloat(getComputedStyle(el).fontSize);
    const rootFontSize = parseFloat(getComputedStyle(document.documentElement).fontSize);
    computedHeight.value = `${px/rootFontSize}rem`;
  }
});

</script>
<style scoped>
</style>
