import MESSAG<PERSON> from 'helpers/message';
import { TYPE_DIFFERENCE_WEIGHT_ENUM } from 'helpers/constants';

const editInventorySchema = {
  type: 'object',
  additionalProperties: false,
  required: ['grossWeightInventory', 'typeDiff'],
  properties: {
    grossWeightInventory: {
      type: 'number',
      exclusiveMinimum: 0,
      maximum: 999999,
      errorMessage: {
        exclusiveMinimum: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    tareWeightInventory: {
      type: 'number',
      minimum: 0,
      maximum: 999999,
      exclusiveMaximum: {
        $data: '1/grossWeightInventory',
      },
      errorMessage: {
        minimum: MESSAGE.MSG_INVENTORY_TARE_WEIGHT_MIN_ERROR,
        maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
        exclusiveMaximum: MESSAGE.MSG_LIMITS_INVENTORY_TAREWEIGHT_MAX_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    groupName: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.MSG_GROUP_SETTING_INVALID,
      },
    },
    typeDiff: {
      type: 'number',
      enum: Object.values(TYPE_DIFFERENCE_WEIGHT_ENUM),
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    reasonDiff: {
      type: 'string',
      minLength: 1,
      maxLength: 300,
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        _: MESSAGE.REASON_DIFFERENCE_INVALID,
      },
    },
  },
  if: {
    properties: {
      typeDiff: {
        const: TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER,
      },
    },
  },
  then: {
    required: ['reasonDiff'],
    properties: {
      reasonDiff: {
        type: 'string',
        maxLength: 300,
        minLength: 1,
        errorMessage: {
          maxLength: MESSAGE.REASON_DIFFERENCE_INVALID,
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
    },
  },
};

export default editInventorySchema;
