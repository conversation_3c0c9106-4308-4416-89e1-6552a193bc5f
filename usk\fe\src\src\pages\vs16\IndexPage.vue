<template>
  <q-card class="input tw:rounded tw:mb-4 tw:bg-white tw:p-4">
    <div
      class="tw:min-h-[60vh] tw:flex tw:flex-col tw:mb-[13rem] tw:tl:mb-[7rem]"
    >
      <div class="tw:text-m-design">
        設定する値を入力して、「確認する」ボタンを押してください。
      </div>
      <div class="tw:my-3 tw:flex tw:flex-col">
        <div class="tw:text-l-design tw:font-bold">アプリ設定</div>
        <div
          class="tw:flex tw:flex-col tw:tl:flex-row tw:justify-between tw:gap-4 tw:my-3"
        >
          <div class="tw:flex tw:flex-col tw:w-full tw:tl:w-[48%] tw:my-4">
            <span class="tw:text-m-design">自動ログアウト</span>
            <div class="tw:flex tw:gap-4 tw:flex-row">
              <q-radio
                class="tw:text-l-design"
                size="4rem"
                v-model="form.enable_session_timeout"
                :val="true"
                label="有効(推奨)"
              />
              <q-radio
                class="tw:text-l-design"
                size="4rem"
                v-model="form.enable_session_timeout"
                :val="false"
                label="無効"
              />
            </div>
          </div>
          <div class="tw:flex tw:flex-col tw:w-full tw:tl:w-[48%] tw:my-4">
            <span class="tw:text-m-design"
              >自動ログアウトまでの時間（1-24）</span
            >
            <div>
              <BaseInput
                v-model.number="form.session_expirytime"
                :class="[
                  {
                    'tw:bg-[#CACACA] tw:border tw:border-[#D2D2D2]':
                      !form.enable_session_timeout,
                    'tw:mb-3 tw:md:mt-0': !errors.enable_session_timeout,
                  },
                  'tw:text-l-design',
                ]"
                input-class="tw:text-right tw:text-l-design"
                type="text"
                inputmode="numeric"
                maxlength="2"
                :error-message="errors.session_expirytime"
                :error="!!errors.session_expirytime"
                outlined
                :disable="!form.enable_session_timeout"
                :mask="{
                  mask: Number, // Chỉ chấp nhận số
                  thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                  scale: 0, // Không cho phép số thập phân
                  signed: false, // Không cho phép số âm
                  min: 0, // Chỉ cho phép số không âm
                  lazy: false, // Hiển thị placeholder ngay lập tức
                  max: 24,
                }"
              >
                <template v-slot:append>
                  <span
                    v-if="form.enable_session_timeout"
                    class="tw:text-m-design tw:mr-[0.5rem] tw:text-[#333333]"
                  >
                    時間
                  </span>
                </template>
              </BaseInput>
            </div>
          </div>
        </div>
        <div
          class="tw:flex tw:flex-col tw:tl:flex-row tw:justify-between tw:gap-4 tw:my-3"
        >
          <div class="tw:flex tw:flex-col tw:w-full tw:tl:w-[48%] tw:my-3">
            <span class="tw:text-m-design">本日の入荷実績を表示</span>
            <div class="tw:flex tw:gap-4 tw:flex-row">
              <q-radio
                class="tw:text-l-design"
                size="4rem"
                v-model="form.display_actual_received"
                :val="true"
                label="表示"
              />
              <q-radio
                class="tw:text-l-design"
                size="4rem"
                v-model="form.display_actual_received"
                :val="false"
                label="非表示"
              />
            </div>
          </div>
          <div class="tw:flex tw:flex-col tw:w-full tw:tl:w-[48%] tw:my-3">
            <span class="tw:text-m-design">QRコードスキャンの初期画面</span>
            <div>
              <BaseSingleSelectInput
                v-model="form.qr_scan_init"
                :input-class="`tw:!text-l-design`"
                :options="optionQrScanInit"
                :use-input="false"
                :clearable="false"
                emit-value
                map-options
              />
            </div>
          </div>
        </div>
      </div>
      <div class="tw:my-3 tw:flex tw:flex-col">
        <div class="tw:text-l-design tw:font-bold">入出荷登録設定</div>
        <div
          class="tw:grid tw:grid-cols-1 tw:tl:grid-cols-2 tw:justify-between tw:gap-y-4 tw:gap-x-14 tw:my-3"
        >
          <div
            class="tw:flex tw:flex-col tw:w-full tw:my-3"
            v-if="
              !CHECK_ROLE(
                [ROLES_ENUM.NORMAL_USER],
                [
                  ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
                  ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
                ],
                [STAFF_TYPE_ENUM.STAFF],
                user,
              )
            "
          >
            <span class="tw:text-m-design">在庫の管理方法</span>
            <div>
              <BaseSingleSelectInput
                v-model="form.inventory_control_type"
                :options="OPTIONS_INVENTORY_CONTROL_TYPE"
                :use-input="false"
                :clearable="false"
                emit-value
                map-options
              />
            </div>
          </div>
          <div class="tw:flex tw:flex-col tw:w-full tw:my-3">
            <span class="tw:text-m-design">デフォルトで表示する出荷先</span>
            <div>
              <BasePartnerSelect
                :error-message="errors.destinationId"
                :error="!!errors.destinationId"
                v-model="form.destination_id"
              />
            </div>
          </div>
          <div class="tw:flex tw:flex-col tw:w-full tw:my-3">
            <span class="tw:text-m-design"
              >シラスウナギの１匹のグラム換算値</span
            >
            <div>
              <BaseInput
                v-model="form.unit_per_gram"
                input-class="tw:text-right
                  tw:font-[400] tw:text-l-design tw:pr-[1rem]"
                type="text"
                inputmode="decimal"
                outlined
                emit-value
                maxlength="6"
                :error-message="errors.unit_per_gram"
                :error="!!errors.unit_per_gram"
                :mask="{
                  mask: Number, // Chỉ chấp nhận số
                  thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                  scale: 2, // Không cho phép số thập phân
                  signed: false, // Không cho phép số âm
                  min: 0, // Chỉ cho phép số không âm
                  lazy: false, // Hiển thị placeholder ngay lập tức
                  radix: '.',
                  max: 999.99,
                  padFractionalZeros: false,
                  normalizeZeros: true,
                }"
              />
            </div>
          </div>
          <div class="tw:flex tw:flex-col tw:w-full tw:my-3">
            <span class="tw:text-m-design">出荷情報の出力方法を指定</span>
            <div>
              <BaseSingleSelectInput
                v-model="form.report_type"
                :options="REPORT_TYPE_OPTIONS"
                :use-input="false"
                :clearable="false"
                emit-value
                map-options
              />
            </div>
          </div>
          <div class="tw:flex tw:flex-col tw:w-full tw:my-3">
            <span class="tw:text-m-design">出荷時に出荷量を表示</span>
            <div class="tw:flex tw:gap-4 tw:flex-row">
              <q-radio
                class="tw:text-l-design"
                size="4rem"
                v-model="form.display_shipment_weight"
                :val="true"
                label="表示"
              />
              <q-radio
                class="tw:text-l-design"
                size="4rem"
                v-model="form.display_shipment_weight"
                :val="false"
                label="非表示"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="tw:my-3 tw:flex tw:flex-col">
        <div class="tw:text-l-design tw:font-bold">伝票設定</div>
        <div
          class="tw:flex tw:flex-col tw:tl:flex-row tw:justify-between tw:gap-4 tw:my-3"
        >
          <div class="tw:flex tw:flex-col tw:w-full tw:tl:w-[48%] tw:my-3">
            <span class="tw:text-m-design">キロ単価設定（万円/kg）</span>
            <div
              class="tw:flex tw:gap-12 tw:tl:gap-4 tw:tl:flex-row tw:flex-col"
            >
              <BaseInput
                v-model="form.price_per_kilogram[0]"
                :error-message="
                  errors.price_per_kilogram ||
                  errors['price_per_kilogram/0'] ||
                  errors.lst_kg
                "
                :error="
                  !!errors.price_per_kilogram ||
                  !!errors['price_per_kilogram/0'] ||
                  !!errors.lst_kg
                "
                input-class="tw:text-right
                tw:font-[400] tw:text-l-design tw:pr-[1rem]"
                inputmode="decimal"
                outlined
                type="text"
                option-value="value"
                maxlength="12"
                :mask="{
                  mask: Number, // Chỉ chấp nhận số
                  thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                  scale: 2, // Không cho phép số thập phân
                  signed: false, // Không cho phép số âm
                  min: 0, // Chỉ cho phép số không âm
                  lazy: false, // Hiển thị placeholder ngay lập tức
                  radix: '.',
                  max: 99999.99,
                  padFractionalZeros: false,
                  normalizeZeros: true,
                }"
              >
              </BaseInput>
              <BaseInput
                v-model="form.price_per_kilogram[1]"
                :error-message="errors['price_per_kilogram/1']"
                :error="
                  !!errors['price_per_kilogram/1'] ||
                  !!errors.price_per_kilogram ||
                  !!errors.lst_kg
                "
                input-class="tw:text-right
                tw:font-[400] tw:text-l-design tw:pr-[1rem]"
                inputmode="decimal"
                outlined
                type="text"
                option-value="value"
                maxlength="12"
                :mask="{
                  mask: Number, // Chỉ chấp nhận số
                  thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                  scale: 2, // Không cho phép số thập phân
                  signed: false, // Không cho phép số âm
                  min: 0, // Chỉ cho phép số không âm
                  lazy: false, // Hiển thị placeholder ngay lập tức
                  radix: '.',
                  max: 99999.99,
                  padFractionalZeros: false,
                  normalizeZeros: true,
                }"
              >
              </BaseInput>
              <BaseInput
                v-model="form.price_per_kilogram[2]"
                :error-message="errors['price_per_kilogram/2']"
                :error="
                  !!errors['price_per_kilogram/2'] ||
                  !!errors.price_per_kilogram ||
                  !!errors.lst_kg
                "
                input-class="tw:text-right
                tw:font-[400] tw:text-l-design tw:pr-[1rem]"
                inputmode="decimal"
                outlined
                type="text"
                option-value="value"
                maxlength="12"
                :mask="{
                  mask: Number, // Chỉ chấp nhận số
                  thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                  scale: 2, // Không cho phép số thập phân
                  signed: false, // Không cho phép số âm
                  min: 0, // Chỉ cho phép số không âm
                  lazy: false, // Hiển thị placeholder ngay lập tức
                  radix: '.',
                  max: 99999.99,
                  padFractionalZeros: false,
                  normalizeZeros: true,
                }"
              >
              </BaseInput>
            </div>
          </div>
          <div class="tw:flex tw:flex-col tw:w-full tw:tl:w-[48%] tw:my-3">
            <span class="tw:text-m-design">尾数単価設定（円/尾）</span>
            <div
              class="tw:flex tw:gap-12 tw:tl:gap-4 tw:tl:flex-row tw:flex-col"
            >
              <BaseInput
                v-model="form.price_per_quantity[0]"
                :error-message="
                  errors.price_per_quantity ||
                  errors['price_per_quantity/0'] ||
                  errors.lst_quantity
                "
                :error="
                  !!errors.price_per_quantity ||
                  !!errors['price_per_quantity/0'] ||
                  !!errors.lst_quantity
                "
                input-class="tw:text-right
                tw:font-[400] tw:text-l-design tw:pr-[1rem]"
                inputmode="numeric"
                outlined
                type="text"
                option-value="value"
                maxlength="6"
                :mask="{
                  mask: Number, // Chỉ chấp nhận số
                  thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                  scale: 2, // Không cho phép số thập phân
                  signed: false, // Không cho phép số âm
                  min: 0, // Chỉ cho phép số không âm
                  lazy: false, // Hiển thị placeholder ngay lập tức
                  radix: '.',
                  max: 999.99,
                  padFractionalZeros: false,
                  normalizeZeros: true,
                }"
              >
              </BaseInput>
              <BaseInput
                v-model="form.price_per_quantity[1]"
                :error-message="errors['price_per_quantity/1']"
                :error="
                  !!errors['price_per_quantity/1'] ||
                  !!errors.price_per_quantity ||
                  !!errors.lst_quantity
                "
                input-class="tw:text-right
                tw:font-[400] tw:text-l-design tw:pr-[1rem]"
                inputmode="numeric"
                outlined
                type="text"
                maxlength="6"
                option-value="value"
                :mask="{
                  mask: Number, // Chỉ chấp nhận số
                  thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                  scale: 2, // Không cho phép số thập phân
                  signed: false, // Không cho phép số âm
                  min: 0, // Chỉ cho phép số không âm
                  lazy: false, // Hiển thị placeholder ngay lập tức
                  radix: '.',
                  max: 999.99,
                  padFractionalZeros: false,
                  normalizeZeros: true,
                }"
              >
              </BaseInput>
              <BaseInput
                v-model="form.price_per_quantity[2]"
                no-error-icon
                :error-message="errors['price_per_quantity/2']"
                :error="
                  !!errors['price_per_quantity/2'] || !!errors.lst_quantity
                "
                input-class="tw:text-right
                tw:font-[400] tw:text-l-design tw:pr-[1rem]"
                inputmode="numeric"
                outlined
                type="text"
                option-value="value"
                maxlength="6"
                :mask="{
                  mask: Number, // Chỉ chấp nhận số
                  thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                  scale: 2, // Không cho phép số thập phân
                  signed: false, // Không cho phép số âm
                  min: 0, // Chỉ cho phép số không âm
                  lazy: false, // Hiển thị placeholder ngay lập tức
                  radix: '.',
                  max: 999.99,
                  padFractionalZeros: false,
                  normalizeZeros: true,
                }"
              >
              </BaseInput>
            </div>
          </div>
        </div>

        <div
          class="tw:flex tw:flex-col tw:tl:flex-row tw:justify-between tw:gap-4 tw:my-3"
        >
          <div class="tw:flex tw:flex-col tw:w-full tw:tl:w-[48%] tw:my-3">
            <span class="tw:text-m-design">内税・外税表記</span>
            <div>
              <BaseSingleSelectInput
                v-model="form.include_tax_type"
                :input-class="`tw:text-l-design!`"
                :options="INCLUDE_TAX_TYPE_OPTIONS"
                :error="!!errors.include_tax_type"
                :error-message="errors.include_tax_type"
                :use-input="false"
                :clearable="false"
                emit-value
                map-options
              />
            </div>
          </div>
          <div class="tw:flex tw:flex-col tw:w-full tw:tl:w-[48%] tw:my-3">
            <span class="tw:text-m-design">伝票の印刷枚数</span>
            <div>
              <BaseSingleSelectInput
                :input-class="`tw:text-l-design!`"
                v-model="form.receipt_number"
                :options="optionsReceiptNumber"
                :error="!!errors.receipt_number"
                :error-message="errors.receipt_number"
                :use-input="false"
                :clearable="false"
                emit-value
                map-options
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </q-card>
  <q-footer
    elevated
    class="tw:bg-white tw:p-3 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)]
    tw:w-full tw:items-center tw:flex tw:justify-center tw:tl:justify-between
    tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
  >
    <BaseButton
      outline
      class="tw:rounded-[40px]"
      :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-[94.5%]`"
      label="トップに戻る"
      @click.prevent="handleClickBackToHome"
    />
    <BaseButton
      outline
      class="tw:rounded-[40px]"
      :class="`tw:bg-blue-3 tw:text-white tw:text-m-design tw:tl:font-bold
      tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-[94.5%]`"
      label="確認する"
      @click.prevent="handleConfirm"
    />
  </q-footer>
</template>
<script setup>
import useValidate from 'composables/validate';
import { CHECK_ROLE, CONVERT_TEXT_SHOW_DEFAULT_SCAN_QR } from 'helpers/common';
import { storeToRefs } from 'pinia';
import commonService from 'services/common.service';
import settingService from 'services/setting.service';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import BaseInput from 'src/components/base/vs/BaseInput.vue';
import BasePartnerSelect from 'src/components/base/vs/BasePartnerSelect.vue';
import BaseSingleSelectInput from 'src/components/base/vs/BaseSingleSelectInput.vue';
import { doParseFloatNumber, FORMAT_NUMBER } from 'src/helpers/common';
import {
  ENTERPRISE_TYPE_ENUM,
  INCLUDE_TAX_TYPE_OPTIONS,
  OPTION_TYPE_ENUM,
  OPTIONS_INVENTORY_CONTROL_TYPE,
  REPORT_TYPE_OPTIONS,
  ROLES_ENUM,
  SHOW_DEFAULT_SCAN_QR_ENUM,
  STAFF_TYPE_ENUM,
} from 'src/helpers/constants';
import updateSettingSchema from 'src/schemas/setting/updateSetting.schema';
import { useAppStore } from 'src/stores/app-store';
import { useAuthStore } from 'src/stores/auth-store';
import { useConfirmFormStore } from 'src/stores/confirm-form-store';
import { computed, onMounted, provide, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const { user } = storeToRefs(useAuthStore());
const { previousRoute } = storeToRefs(useAppStore());
const { setConfirmData, getConfirmData } = useConfirmFormStore();

const form = ref({
  destination_id: '',
  enable_session_timeout: true,
  price_per_kilogram: [],
  price_per_quantity: [],
  display_shipment_weight: false,
  display_actual_received: false,
  qr_scan_init: '',
  receipt_number: 1,
  report_type: '',
  unit_per_gram: '',
  inventory_control_type: '',
  session_expirytime: null,
  include_tax_type: '',
});
const listPartner = ref([]);
const optionsPartner = ref([]);
const { validateData, errors } = useValidate();

const handleConfirm = () => {
  const payload = {
    ...form.value,
    price_per_kilogram: form.value.price_per_kilogram.map(price =>
      price ? doParseFloatNumber(price) : ''),
    price_per_quantity: form.value.price_per_quantity.map(price =>
      price ? doParseFloatNumber(price) : ''),
    lst_quantity: form.value.price_per_quantity
      .map(price => (price ? doParseFloatNumber(price) : ''))
      .filter(price => price !== ''),
    lst_kg: form.value.price_per_kilogram
      .map(price => (price ? doParseFloatNumber(price) : ''))
      .filter(price => price !== ''),
    session_expirytime: form.value.enable_session_timeout
      ? form.value.session_expirytime
      : undefined,
    destination_id: form.value.destination_id || undefined,
  };

  const isValid = validateData(updateSettingSchema, payload);

  if (!isValid) {
    return;
  }

  setConfirmData({
    ...payload,
    price_per_quantity: payload.lst_quantity,
    price_per_kilogram: payload.lst_kg,
    destination_name: listPartner.value.find(
      partner => partner.value === form.value.destination_id
    )?.label,
  });
  router.push({ name: 'settingConfirm' });
};

const handleClickBackToHome = () => {
  router.push({ name: 'home' });
};

watch(
  () => form.value.enable_session_timeout,
  val => {
    if (!val) {
      form.value.session_expirytime = null;
    }
  }
);

const optionsReceiptNumber = computed(() =>
  Array.from({ length: 5 }, (_, i) => ({ label: i + 1, value: i + 1 })));

const optionQrScanInit = computed(() => {
  const values = Object.values(SHOW_DEFAULT_SCAN_QR_ENUM);
  return values
    .filter(val =>
      CHECK_ROLE(
        [ROLES_ENUM.NORMAL_USER],
        [ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        [],
        user.value
      )
        ? val !== SHOW_DEFAULT_SCAN_QR_ENUM.USE_USER_ID
        : true)
    .map(value => ({
      label: CONVERT_TEXT_SHOW_DEFAULT_SCAN_QR(value),
      value,
    }));
});

onMounted(async () => {
  const partnerOptionsResponse = await commonService.getOptions({
    type: OPTION_TYPE_ENUM.USER_SHIPPER,
  });
  optionsPartner.value = partnerOptionsResponse.payload;
  listPartner.value = partnerOptionsResponse.payload;

  const settingStoreData = getConfirmData();
  if (settingStoreData && previousRoute.value.name === 'settingConfirm') {
    const { ...rest } = settingStoreData;
    form.value = {
      ...rest,
      destination_id: settingStoreData.destination_id || '',
      price_per_kilogram: Array.from(
        { length: 3 },
        (_, i) => FORMAT_NUMBER(settingStoreData.price_per_kilogram[i]) || ''
      ),
      price_per_quantity: Array.from(
        { length: 3 },
        (_, i) => FORMAT_NUMBER(settingStoreData.price_per_quantity[i]) || ''
      ),
      unit_per_gram: FORMAT_NUMBER(settingStoreData.unit_per_gram) || '',
    };
  } else {
    const settingResponse = await settingService.getSetting();

    const settingData = settingResponse.payload;

    form.value = {
      destination_id: settingData.destination_id || '',
      enable_session_timeout: settingData.enable_session_timeout,
      price_per_kilogram: Array.from(
        { length: 3 },
        (_, i) => FORMAT_NUMBER(settingData.price_per_kilogram[i]) || ''
      ),
      price_per_quantity: Array.from(
        { length: 3 },
        (_, i) => FORMAT_NUMBER(settingData.price_per_quantity[i]) || ''
      ),
      display_shipment_weight: settingData.display_shipment_weight,
      display_actual_received: settingData.display_actual_received,
      qr_scan_init:
        settingData.qr_scan_init || SHOW_DEFAULT_SCAN_QR_ENUM.USE_CAMERA,
      receipt_number: settingData.receipt_number,
      report_type: settingData.report_type,
      unit_per_gram: FORMAT_NUMBER(settingData.unit_per_gram) || '',
      inventory_control_type: settingData.inventory_control_type,
      session_expirytime: settingData.enable_session_timeout
        ? settingData.session_expirytime
        : null,
      include_tax_type: settingData.include_tax_type,
    };
  }
});

provide('basePartnerSelectProvideData', { listPartner: optionsPartner });
</script>

<style scoped>
:deep(.q-field--outlined .q-field__control) {
  padding-right: 0;
}

:deep(.q-field__control-container .q-field__suffix) {
  margin-right: 1rem !important;
}
</style>
