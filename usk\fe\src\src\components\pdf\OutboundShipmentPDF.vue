<template>
  <div class="tw:hidden">
    <div id="shipment-pdf-file-data" class="">
      <h1 class="tw:text-[50px] tw:font-bold tw:text-center">
        シラスウナギ出荷情報
      </h1>
      <p class="tw:text-right tw:text-[28px] tw:py-[32px] tw:px-2 tw:font-bold">
        {{ `出荷日: ${provideData.shipping_date}` }}
      </p>
      <div class="tw:border">
        <div class="row-item tw:border-b">
          <span class="label">漁獲/荷口番号</span>
          <span
            class="value"
            >{{ maskCodeString(provideData.code) }}</span
          >
        </div>
        <div class="row-item tw:border-b">
          <span class="label">品目</span>
          <span class="value"
            >シラスウナギ</span
          >
        </div>
        <div class="row-item tw:border-b">
          <span class="label">事業者名</span>
          <div
            class="tw:max-w-[490px] dynamic-font-size"
          >
            {{ provideData.starting_enterprise }}
          </div>
        </div>
        <div class="tw:flex">
          <div class="tw:flex-1">
            <div
              :class="`row-item ${
                !!provideData.weight ? 'tw:border-b' : ''
              }`"
            >
              <span class="label"
                >出荷先(届出事業者)</span
              >
              <div
                class="tw:max-w-[490px] dynamic-font-size"
              >
                {{ provideData.destination_enterprise }}
              </div>
            </div>
            <div v-show="!!provideData.weight" class="row-item">
              <span class="label">出荷量</span>
              <span class="value"
                >{{ FORMAT_NUMBER(provideData.weight) }}g</span
              >
            </div>
          </div>
          <div class="tw:border" v-if="qrCode.value">
            <img
              alt=""
              id="shipment-qr-code"
              :src="qrCode.value"
              spinner-color="white"
              class="tw:h-[180px] tw:w-[180px] tw:aspect-square tw:max-w-none"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, inject, watch } from 'vue';
import { useQRCode } from '@vueuse/integrations/useQRCode';
import { maskCodeString, FORMAT_NUMBER } from 'helpers/common';

const provideData = inject('outBoundShipmentPDFProvideData');
const qrCode = computed(() => (provideData.value.qr_code ? useQRCode(`${process.env.SITE_URL}/shipping/${provideData.value.qr_code}`) : ''));

const adjustFontSize = () => {
  const spans = document.querySelectorAll('.dynamic-font-size');
  if (provideData.value.starting_enterprise?.length < 31) {
    spans[0].style.fontSize = '32px';
    spans[0].style.lineHeight = '40px';
  } else {
    spans[0].style.fontSize = '16px';
    spans[0].style.lineHeight = '20px';
    spans[0].style.transform = 'translateY(-8px)';
  }

  if (provideData.value.destination_enterprise?.length < 31) {
    spans[1].style.fontSize = '32px';
    spans[1].style.lineHeight = '40px';
  } else {
    spans[1].style.fontSize = '16px';
    spans[1].style.lineHeight = '20px';
    spans[1].style.transform = 'translateY(-8px)';
  }
};

watch(
  () => provideData.value.starting_enterprise?.length,
  () => {
    adjustFontSize();
  }
);

</script>

<style scoped>
.row-item {
  display: flex;
  min-height: 90px;
}

.label {
  font-size: 20px;
  padding: 0 10px;
  width: 200px !important;
}

.value {
  display: flex;
  align-items: center;
  font-size: 32px;
  line-height: 40px;
  padding: 0;
  margin: 0;
  transform: translateY(-14px);
}

.dynamic-font-size {
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0;
  transform: translateY(-14px);
}
</style>
