<template>
  <PopupConfirm
    v-model="isShowPopupConfirmVolume"
    label="修正確認"
    description="以下の内容で、出荷実績を修正します"
    labelSubmit="登録する"
    labelCancel="入力内容を修正する"
    @onClickCancel="isShowPopupConfirmVolume = false"
    @onClickSubmit="handleClickSubmitVolume"
  >
    <div class="tw:text-m-design tw:p-5 tw:pt-0">
      <div class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA]
        tw:border tw:border-[#E0E0E0]">
        <div class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center">
          <span>
            出荷登録単位
          </span>
        </div>
        <div class="tw:tl:w-[70%] tw:flex tw:gap-4 tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]">
          <span>重量</span>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA]
        tw:border tw:border-t-0 tw:border-[#E0E0E0]"
        v-if="
          form.volumeType === UNIT_TYPE_SETTING_ENUM.WEIGHT_ONLY
        "
      >
        <div class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between">
          <span>
            出荷量
          </span>
          <q-badge class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2">
            必須
          </q-badge>
        </div>
        <div class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-5 tw:tl:py-2 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]">
          <div class="tw:flex tw:tl:gap-12">
            <div class="tw:flex tw:gap-3 tw:tl:items-center tw:flex-col tw:tl:flex-row">
              <!-- 全体重量 -->
              <div>
                <span class="tw:text-m-design">
                  {{ '000,000.00' }}
                </span>
                <span class="tw:text-xs-design"> g </span>
              </div>
            </div>
            <div class="tw:flex tw:items-center tw:relative">
              <!-- subtraction sign sm -->
              <div
                class="tw:h-1 tw:bg-[#7E8093] tw:px-4 tw:mt-2 tw:mx-5 tw:tl:hidden"
              />
              <div class="tw:flex-1 tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:gap-3">
                <!-- subtraction sign tl -->
                <div
                  class="tw:h-0.75 tw:bg-[#7E8093] tw:px-3 tw:mx-5 tw:hidden
                    tw:tl:block tw:absolute tw:top-[1.5rem] tw:-left-[3.25rem]"
                />
                <!-- 風袋 -->
                <div>
                  <span class="tw:text-m-design">
                    {{ '000,000.00' }}
                  </span>
                  <span class="tw:text-xs-design"> g </span>
                </div>
              </div>
            </div>
          </div>
          <div class="tw:flex tw:items-center tw:gap-4">
            <!-- 出荷量 -->
            <span class="tw:text-xs-design">出荷量</span>
            <div class="tw:font-bold">
              <span class="tw:text-m-design">
                {{ '000,000.00' }}
              </span>
              <span class="tw:text-xs-design"> g </span>
            </div>
          </div>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA]
        tw:border tw:border-t-0 tw:border-[#E0E0E0]"
        v-else
      >
        <div class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between">
          <span>
            入荷量
          </span>
          <q-badge class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2">
            必須
          </q-badge>
        </div>
        <div class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-2 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]">
          <div class="tw:tl:col-span-1 tw:flex tw:flex-col tw:tl:flex-row tw:gap-3 tw:tl:items-center">
            <!-- 入荷量 -->
            <div>
                <span class="tw:text-m-design">
                  {{ '000,000.00' }}
                </span>
                <span class="tw:text-xs-design"> 尾 </span>
              </div>
          </div>
        </div>
      </div>
      <div class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA]
        tw:border tw:border-t-0 tw:border-[#E0E0E0]">
        <div class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between">
          <span>
            出荷日
          </span>
          <q-badge class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2">
            必須
          </q-badge>
        </div>
        <div class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]
          tw:flex tw:items-center tw:min-h-[4.25rem]">
          <span>
            {{ FORMAT_DATE(form.date) }}
          </span>
        </div>
      </div>
    </div>
  </PopupConfirm>
  <PopupConfirm
    v-model="isShowPopupConfirmReason"
    label="出荷量の差異"
    description="在庫量を大きく越える出荷量が指定されています。 差異発生の理由をチェックしてください"
    labelSubmit="差異の理由を登録する"
    labelCancel="出荷量を修正する"
    @onClickCancel="isShowPopupConfirmReason = false"
    @onClickSubmit="handleClickSubmitReason"
  >
    <div class="tw:text-m-design tw:p-5 tw:pt-0">
      <div>
          <div
            class="tw:flex tw:items-center"
          >
            <span> 差異の理由 </span>
            <q-badge
              class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
            >
              必須
            </q-badge>
          </div>
          <div
            class="tw:flex tw:gap-4 tw:py-1"
          >
            <q-radio
              v-for="(item, index) in reasonOptions"
              v-model="form.typeDiff"
              :key="index"
              :val="item.value"
              size="3.75rem"
              class="tw:transform tw:-translate-x-3"
            >
              <span class="tw:text-m-design tw:text-[#333333]">
                {{ item.label }}
              </span>
            </q-radio>
          </div>
      </div>
      <div>
          <div
            class="tw:flex tw:items-center"
          >
            <span> 「その他」を選択した理由 </span>
            <q-badge
              class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
            >
              必須
            </q-badge>
          </div>
          <div
            class="tw:flex tw:gap-4 tw:py-1"
          >
            <q-input
              :class="[
                {
                  'tw:bg-[#CACACA] tw:border tw:border-[#D2D2D2]':
                    form.typeDiff !== TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER,
                },
                'tw:text-m-design tw:w-full',
              ]"
              :disable="form.typeDiff !== TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER"
              v-model="form.reasonDiff"
              outlined
              autocomplete="nope"
              type="textarea"
              maxlength="300"
              no-error-icon
              hide-bottom-space
            />
          </div>
      </div>
    </div>
  </PopupConfirm>
  <q-page class="tw:flex tw:flex-col tw:h-full tw:pb-[22rem] tw:tl:pb-[8rem]">
    <q-card class="tw:p-5 tw:flex tw:flex-col tw:h-full">
      <div>
        <h2 class="tw:text-l-design tw:font-bold">出荷実績詳細</h2>
      </div>
      <div class="tw:text-m-design tw:mt-5">
        <div class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA]
          tw:border tw:border-[#E0E0E0]">
          <div class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center">
            <span>
              漁獲/荷口番号
            </span>
          </div>
          <div class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]">
            <span>{{ maskCodeString(data.code) }}</span>
          </div>
        </div>
        <div class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA]
          tw:border tw:border-t-0 tw:border-[#E0E0E0]">
          <div class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center">
            <span>
              出荷先
            </span>
          </div>
          <div class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]">
            <span>
              {{
                `${
                  data.destination_user?.name ||
                  data.destination_enterprise?.enterprise_name
                }`
              }}
            </span>
          </div>
        </div>
        <div class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA]
          tw:border tw:border-t-0 tw:border-[#E0E0E0]">
          <div class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center">
            <span>
              出荷登録単位
            </span>
          </div>
          <div class="tw:tl:w-[70%] tw:flex tw:gap-4 tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]">
            <q-radio
              v-for="(item, index) in INPUT_VOLUME_TYPE_OPTIONS"
              v-model="form.volumeType"
              :key="index"
              :val="item.value"
              size="3.75rem"
              class="tw:transform tw:-translate-x-3"
            >
              <span class="tw:text-m-design tw:text-[#333333]">
                {{ item.label }}
              </span>
            </q-radio>
          </div>
        </div>
        <div
          class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA]
          tw:border tw:border-t-0 tw:border-[#E0E0E0]"
          v-if="
            form.volumeType === UNIT_TYPE_SETTING_ENUM.WEIGHT_ONLY
          "
        >
          <div class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between">
            <span>
              出荷量
            </span>
            <q-badge class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2">
              必須
            </q-badge>
          </div>
          <div class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-5 tw:tl:py-2 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]">
            <div class="tw:grid tw:grid-cols-1 tw:gap-x-10 tw:gap-y-3 tw:tl:grid-cols-2">
              <div class="tw:flex tw:gap-3 tw:tl:items-center tw:flex-col tw:tl:flex-row">
                <!-- 全体重量 -->
                <span class="tw:text-xs-design tw:min-w-[6rem]">全体重量</span>
                <BaseInput
                  clearable
                  type="text"
                  maxlength="10"
                  inputmode="numeric"
                  autocomplete="nope"
                  :model-value="form.grossWeight"
                  @update:model-value="handleInputGrossWeight"
                  v-cleave="{
                    numeral: true,
                    numeralPositiveOnly: true,
                  }"
                  outlined
                  class="tw:flex-1"
                  input-class="tw:text-right tw:text-m-design"
                >
                  <template v-slot:append>
                    <div
                      :class="`tw:text-[#333333] tw:pr-4 tw:tl:pr-8 tw:text-xs-design tw:mt-2`"
                    >
                      g
                    </div>
                  </template>
                </BaseInput>
              </div>
              <div class="tw:flex tw:items-center tw:relative">
                <!-- subtraction sign sm -->
                <div
                  class="tw:h-1 tw:bg-[#7E8093] tw:px-4 tw:mt-12 tw:mx-5 tw:tl:hidden"
                />
                <div class="tw:flex-1 tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:gap-3">
                  <!-- subtraction sign tl -->
                  <div
                    class="tw:h-0.75 tw:bg-[#7E8093] tw:px-3 tw:mb-8 tw:mx-5 tw:hidden
                      tw:tl:block tw:absolute tw:top-[2rem] tw:-left-[3.25rem]"
                  />
                  <!-- 風袋 -->
                  <span class="tw:text-xs-design tw:min-w-[3rem]">風袋</span>
                  <BaseInput
                    clearable
                    type="text"
                    maxlength="10"
                    inputmode="numeric"
                    autocomplete="nope"
                    :model-value="form.tareWeight"
                    @update:model-value="handleInputTareWeight"
                    v-cleave="{
                      numeral: true,
                      numeralPositiveOnly: true,
                    }"
                    outlined
                    class="tw:flex-1"
                    input-class="tw:text-right tw:text-m-design"
                  >
                    <template v-slot:append>
                      <div
                        :class="`tw:text-[#333333] tw:pr-4 tw:tl:pr-8 tw:text-xs-design tw:mt-2`"
                      >
                        g
                      </div>
                    </template>
                  </BaseInput>
                </div>
              </div>
              <!-- Divider -->
              <div class="tw:tl:col-span-2 tw:h-[1px] tw:bg-[#CBCBCB] tw:mt-2 tw:md:hidden"/>
              <div class="tw:tl:col-span-2 tw:flex tw:items-center tw:justify-between tw:md:justify-start tw:gap-4">
                <!-- 出荷量 -->
                <span class="tw:text-xs-design">出荷量</span>
                <div class="tw:font-bold">
                  <span class="tw:text-m-design">
                    {{ '000,000.00' }}
                  </span>
                  <span class="tw:text-xs-design"> g </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA]
          tw:border tw:border-t-0 tw:border-[#E0E0E0]"
          v-else
        >
          <div class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between">
            <span>
              入荷量
            </span>
            <q-badge class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2">
              必須
            </q-badge>
          </div>
          <div class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-2 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]">
            <div class="tw:tl:col-span-1 tw:flex tw:flex-col tw:tl:flex-row tw:gap-3 tw:tl:items-center">
              <!-- 入荷量 -->
              <span class="tw:text-xs-design">入荷量</span>
              <BaseInput
                clearable
                type="text"
                maxlength="10"
                inputmode="numeric"
                autocomplete="nope"
                :model-value="form.quantity"
                @update:model-value="handleInputQuantity"
                v-cleave="{
                  numeral: true,
                  numeralPositiveOnly: true,
                }"
                outlined
                input-class="tw:text-right tw:text-m-design"
              >
                <template v-slot:append>
                  <div
                    :class="`tw:text-[#333333] tw:pr-4 tw:tl:pr-8 tw:text-xs-design tw:mt-2`"
                  >
                    尾
                  </div>
                </template>
              </BaseInput>
            </div>
          </div>
        </div>
        <div class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA]
          tw:border tw:border-t-0 tw:border-[#E0E0E0]">
          <div class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between">
            <span>
              出荷日
            </span>
            <q-badge class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2">
              必須
            </q-badge>
          </div>
          <div class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-1.5 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]">
            <BaseDatePicker
              v-model="form.date"
              class="tw:max-w-[22rem] tw:tl:transform tw:tl:translate-y-0.5"
              input-class="tw:text-m-design tw:text-[#333333]"
            />
          </div>
        </div>
      </div>
    </q-card>

    <q-footer
      elevated
      class="tw:bg-white tw:p-4 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)]
      tw:w-full tw:tl:justify-between tw:flex tw:justify-center tw:mt-4 tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        outline

        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-[#004AB9] tw:text-m-design tw:tl:font-bold
        tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem] tw:min-w-[18.9rem]`"
        label="修正をやめる"
        @click="handleClickCancel"
      />
      <BaseButton

        class="tw:rounded-[40px]"
        :class="`tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:font-bold
        tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem] tw:min-w-[18.9rem]`"
        label="登録する"
        @click="handleClickConfirm"
      />
    </q-footer>
  </q-page>
</template>
<script setup>
import BaseDatePicker from 'components/base/vs/BaseDatePicker.vue';
import {
  FORMAT_DATE,
  FORMAT_NUMBER,
  doParseFloatNumber,
  isNumeric,
  maskCodeString,
} from 'helpers/common';
import { storeToRefs } from 'pinia';
import shippingService from 'services/shipping.service';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import BaseInput from 'src/components/base/vs/BaseInput.vue';
import {
  TYPE_DIFFERENCE_WEIGHT_ENUM,
  UNIT_TYPE_SETTING_ENUM,
} from 'src/helpers/constants';
import { useAppStore } from 'stores/app-store';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import PopupConfirm from './components/PopupConfirm.vue';

// #region states
const { settingUser } = storeToRefs(useAppStore());
const router = useRouter();
const data = ref({});
const form = ref({
  destinationId: '',
  grossWeight: '',
  netWeight: '',
  tareWeight: '',
  quantity: '',
  date: '',
  volumeType: UNIT_TYPE_SETTING_ENUM.WEIGHT_ONLY,
  typeDiff: TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR,
  reasonDiff: '',
});
const isShowPopupConfirmVolume = ref(false);
const isShowPopupConfirmReason = ref(false);
const unitPerGram = ref(settingUser.value?.unit_per_gram ?? 0);
const INPUT_VOLUME_TYPE_OPTIONS = [
  { label: '重量', value: UNIT_TYPE_SETTING_ENUM.WEIGHT_ONLY },
  { label: '尾数', value: UNIT_TYPE_SETTING_ENUM.QUANTITY_ONLY },
];
const reasonOptions = ref([
  { label: '計量誤差', value: TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR },
  { label: 'その他', value: TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER },
]);
// #endregion

// #region functions
const handleInputGrossWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const grossWeightNum = doParseFloatNumber(newValue || 0);
  const tareWeightNum = doParseFloatNumber(
    form.value.tareWeight || 0
  );
  const netWeightNum = grossWeightNum - tareWeightNum;
  form.value.grossWeight = newValue
    ? FORMAT_NUMBER(grossWeightNum)
    : newValue;

  form.value.netWeight = netWeightNum >= 0 ? FORMAT_NUMBER(netWeightNum) : undefined;
  form.value.quantity = netWeightNum >= 0
    ? FORMAT_NUMBER(
      Math.ceil((netWeightNum / unitPerGram.value).toFixed(3))
    )
    : undefined;
};

const handleInputTareWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const grossWeightNum = doParseFloatNumber(
    form.value.grossWeight || 0
  );
  const tareWeightNum = doParseFloatNumber(newValue || 0);
  const netWeightNum = grossWeightNum - tareWeightNum;
  form.value.tareWeight = newValue
    ? FORMAT_NUMBER(tareWeightNum)
    : newValue;

  form.value.netWeight = netWeightNum >= 0 ? FORMAT_NUMBER(netWeightNum) : undefined;
  form.value.quantity = netWeightNum >= 0
    ? FORMAT_NUMBER(
      Math.ceil((netWeightNum / unitPerGram.value).toFixed(3))
    )
    : undefined;
};

const handleInputQuantity = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const quantityNum = doParseFloatNumber(newValue || 0);
  form.value.tareWeight = undefined;
  form.value.quantity = newValue
    ? FORMAT_NUMBER(quantityNum, 0)
    : newValue;
  const netWeightNum = Math.ceil(
    doParseFloatNumber(form.value.quantity)
        * (unitPerGram.value * 100)
  ) / 100;
  form.value.netWeight = doParseFloatNumber(form.value.quantity)
    ? FORMAT_NUMBER(netWeightNum)
    : undefined;
  form.value.grossWeight = doParseFloatNumber(form.value.quantity)
    || doParseFloatNumber(form.value.quantity) === 0
    ? FORMAT_NUMBER(netWeightNum)
    : undefined;
  if (newValue === '') {
    form.value.grossWeight = '';
  }
};

const handleClickConfirm = async () => {
  // TODO: validate form
  isShowPopupConfirmVolume.value = true;
};

const handleClickSubmitVolume = async () => {
  // TODO: handle submit logic
  isShowPopupConfirmVolume.value = false;

  // TODO: need check volume to show confirm reason popup
  form.value.reasonDiff = '';
  form.value.typeDiff = TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR;
  isShowPopupConfirmReason.value = true;
};

const handleClickSubmitReason = async () => {
  // TODO: handle submit logic
  isShowPopupConfirmReason.value = false;
};

const handleClickCancel = () => {
  router.back();
};
// #endregion

onMounted(async () => {
  const shippingDetail = await shippingService.getShippingDetail(router.currentRoute.value.params?.id);
  if (shippingDetail.code !== 0 && shippingDetail.code !== 401) {
    await router.push({
      name: 'home',
    });
  }
  data.value = shippingDetail.payload;
  form.value = {
    destinationId: data.value.destination_user?.id,
    grossWeight: FORMAT_NUMBER(data.value.shipping_gross_weight),
    tareWeight: FORMAT_NUMBER(data.value.shipping_tare_weight),
    netWeight: FORMAT_NUMBER(data.value.shipping_net_weight),
    quantity: FORMAT_NUMBER(data.value.shipping_quantity),
    date: FORMAT_DATE(data.value.shipping_date),
    // TODO: get form api
  };
});
</script>

<style scoped>
:deep(.q-field--outlined .q-field__control) {
  padding-right: 0;
}
</style>
