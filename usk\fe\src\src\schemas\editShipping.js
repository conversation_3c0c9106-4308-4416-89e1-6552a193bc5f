import MESSAGE from 'helpers/message';

const editShipping = {
  additionalProperties: false,
  type: 'object',
  required: ['destinationId', 'shippingGrossWeight', 'shippingDate'],
  properties: {
    destinationId: {
      type: 'integer',
      minimum: 1,
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    shippingGrossWeight: {
      type: 'number',
      exclusiveMinimum: 0,
      maximum: 999999,
      errorMessage: {
        exclusiveMinimum: MESSAGE.MSG_LIMITS_GROSSWEIGHT_MIN_ERROR,
        maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    shippingTareWeight: {
      type: 'number',
      minimum: 0,
      maximum: 999999,
      exclusiveMaximum: {
        $data: '1/shippingGrossWeight',
      },
      errorMessage: {
        minimum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MIN_ERROR,
        maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
        exclusiveMaximum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MAX_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    shippingQuantity: {
      type: 'number',
      minimum: 1,
      maximum: 9999999,
      errorMessage: {
        minimum: MESSAGE.MSG_LIMITS_QUANTITY_MIN_ERROR,
        maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    shippingDate: {
      type: 'string',
      format: 'slash-date',
      minLength: 1,
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
  },
  errorMessage: {
    required: {
      name: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
    },
  },
};

export default editShipping;
