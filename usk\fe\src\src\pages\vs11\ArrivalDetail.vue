<template>
  <div class="tw:my-4 tw:pb-[20rem] tw:tl:pb-0 tw:dt:pb-[8rem]">
    <q-card
      class="tw:mt-4 tw:mb-4 tw:bg-white tw:p-4 tw:min-h-[calc(100vh-15rem)] tw:text-[#333333]"
    >
      <div class="tw:text-l-design tw:font-bold tw:mb-4">入荷実績詳細</div>
      <div class="tw:border tw:border-[#E0E0E0] tw:rounded-none tw:mb-[8rem]">
        <div class="tw:flex tw:flex-col tw:divide-y tw:divide-[#E0E0E0]">
          <!-- code -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              漁獲/荷口番号
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              {{ maskCodeString(detailArrival.code) }}
            </div>
          </div>

          <!-- Supplier -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              仕入先（届出事業者）
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              {{ `${detailArrival.starting_enterprise_name}` }}
            </div>
          </div>

          <!-- test -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              仕入先
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              {{
                `${
                  detailArrival.starting_user_name
                }`
              }}
            </div>
          </div>

          <!-- license_number -->
          <div
          v-if="user.enterprise_type === ROLES_ENUM_OPTIONS_VALUES.CATCH_ENTERPRISE || user.enterprise_type === ROLES_ENUM_OPTIONS_VALUES.CATCH_STAFF"
          class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5
                tw:p-4 tw:font-[400] tw:text-m-design
                tw:items-center tw:flex tw:justify-between">
              許可番号
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              {{
                `${
                  detailArrival.starting_license_number
                }`
              }}
            </div>
          </div>

          <!-- note 1 -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5
                tw:p-4 tw:font-[400] tw:text-m-design
                tw:items-center tw:flex tw:justify-between">
              備考１
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              {{
                `${
                  detailArrival.starting_user_note_1
                }`
              }}
            </div>
          </div>

          <!-- note 2 -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5
                tw:p-4 tw:font-[400] tw:text-m-design
                tw:items-center tw:flex tw:justify-between">
              備考２
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              {{
                `${
                  detailArrival.starting_user_note_2
                }`
              }}
            </div>
          </div>

          <!-- destination user name -->
          <div
          v-if="user.role === ROLES_ENUM_OPTIONS_VALUES.CATCH_ENTERPRISE && user.staff_type === STAFF_TYPE_ENUM.STAFF ||
          user.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE && user.staff_type === STAFF_TYPE_ENUM.ENTERPRISE"
          class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5
                tw:p-4 tw:font-[400] tw:text-m-design
                tw:items-center tw:flex tw:justify-between">
              入荷者
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              {{
                `${
                  detailArrival.destination_user_name
                }`
              }}
            </div>
          </div>

          <!-- destination user name -->
          <div
          v-if="detailArrival.qr_code && detailArrival.type_diff"
          class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5
                tw:p-4 tw:font-[400] tw:text-m-design
                tw:items-center tw:flex tw:justify-between">
              差異の理由
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              {{
                `${
                  detailArrival.reason_diff
                }`
              }}
            </div>
          </div>

          <!-- Destination enterprise name -->
          <div
          v-if="detailArrival.shipping_type === SHIPPING_TYPE_ENUM.ARRIVAL_MANUAL ||
          detailArrival.shipping_type === SHIPPING_TYPE_ENUM.PROXY"
          class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              入荷登録単位
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              {{ maskVolumeTypeToDisplay(detailArrival.setting) }}
            </div>
          </div>

          <!-- arrival weight -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              入荷量
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              <div
              v-if="detailArrival.arrival_quantity && detailArrival.shipping_type === SHIPPING_TYPE_ENUM.ARRIVAL_MANUAL ||
              detailArrival.arrival_quantity && detailArrival.shipping_type === SHIPPING_TYPE_ENUM.PROXY">
                {{ FORMAT_NUMBER(detailArrival.arrival_quantity) }} 尾
              </div>
              <div>
                {{ FORMAT_NUMBER(detailArrival.arrival_net_weight) }} g
              </div>
            </div>
          </div>

          <!-- shipping date -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              出荷日
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              {{ FORMAT_DATE(detailArrival.shipping_date) }}
            </div>
          </div>

          <!-- arrival date -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              入荷日
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              {{ FORMAT_DATE(detailArrival.arrival_date) }}
            </div>
          </div>
        </div>
      </div>

      <!-- Buttons -->
      <q-footer
        elevated
        class="tw:bg-white tw:p-3 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)]
        tw:w-full tw:items-center tw:flex tw:justify-center tw:tl:justify-between
         tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
      >
        <BaseButton
          outline
          class="tw:rounded-[40px]"
          :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
          tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem]
            tw:h-[4.75rem] tw:w-ful`"
          label="取引先管理に戻る" @click.prevent="goToPage('arrivalList')"/>
        <div class="tw:gap-4 tw:flex tw:flex-col tw:tl:flex-row tw:w-full tw:tl:w-[50rem] tw:justify-end">
          <BaseButton outline  class="tw:rounded-[40px]" v-if="detailArrival.shipping_type === SHIPPING_TYPE_ENUM.PROXY"
            :class="`tw:bg-blue-3 tw:text-white tw:text-m-design tw:tl:font-bold
            tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem]
            tw:h-[4.75rem] tw:w-ful`"
            label="伝票を印刷する" @click.prevent="printReport"/>
          <BaseButton outline  class="tw:rounded-[40px]"
            v-if="user.status !== OPTIONS_STATUS_USER[1].value && checkCanceFromDate &&
            detailArrival.inventory?.is_history_cancel_locked"
            :class="`tw:bg-white tw:text-[#E80F00] tw:text-m-design tw:tl:font-bold
            tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem]
            tw:h-[4.75rem] tw:w-ful`"
            label="削除する" @click.prevent="cancelArrival"/>
          <BaseButton outline  class="tw:rounded-[40px]"
            v-if="user.status !== OPTIONS_STATUS_USER[1].value && checkCanceFromDate &&
            detailArrival.inventory?.is_history_cancel_locked"
            :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
            tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem]
            tw:h-[4.75rem] tw:w-ful`"
            label="修正する" @click.prevent="pushToEdit"/>
        </div>
      </q-footer>
    </q-card>
  </div>
  <PopupConfirmText />
</template>
<script setup>
// #region import
import { ref, onMounted, provide } from 'vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter } from 'vue-router';
import PopupConfirmText from 'components/PopupConfirmText.vue';
import arrivalService from 'src/shared/services/arrival.service';
import { maskCodeString, FORMAT_DATE, FORMAT_NUMBER, FORMAT_DATE_TIME_UTC } from 'helpers/common';
import MESSAGE from 'helpers/message';
import toast from 'utilities/toast';
import { makeQuantityXML, makeXML } from 'boot/print';
import { ENTERPRISE_TYPE_ENUM, ROLES_ENUM_OPTIONS_VALUES,
  SHIPPING_TYPE_ENUM, STAFF_TYPE_ENUM, OPTIONS_STATUS_USER,
  ROLES_ENUM, INPUT_VOLUME_SHIPMENT_TYPE_OPTIONS } from 'src/helpers/constants';
import dayjs from 'boot/dayjs';
import { useAuthStore } from 'src/stores/auth-store';
import BaseButton from 'src/components/base/vs/BaseButton.vue';

// #endregion import

// #region variable
const { colorSub, role } = storeToRefs(useAppStore());
const router = useRouter();
const isEdit = ref(false);
const isPrint = ref(false);
const isStaff = ref(false);
const isShowMoreInfo = ref(false);
const detailArrival = ref({});
const arrivalId = ref();
const isPopupConfirmCancel = ref(false);
const {user} = storeToRefs(useAuthStore());
const checkCanceFromDate = ref();

// #endregion variable

// #region function
const printReport = async () => {
  if (detailArrival.value.setting?.price_per_kilogram) {
  const pricePerKilogram = detailArrival.value.setting?.price_per_kilogram || 0;
  const price =
    Number(detailArrival.value.arrival_net_weight) * Number(pricePerKilogram);
  const dataPrint = makeXML(
    detailArrival.value.destination_enterprise_name,
    detailArrival.value.destination_user_name,
    maskCodeString(detailArrival.value.code?.replaceAll('-', '')),
    FORMAT_NUMBER(detailArrival.value?.arrival_gross_weight),
    FORMAT_NUMBER(detailArrival.value?.arrival_tare_weight),
    FORMAT_NUMBER(detailArrival.value?.arrival_net_weight),
    // map utc to local time
    dayjs(FORMAT_DATE(detailArrival.value.arrival_date)).toDate(),
    pricePerKilogram || '',
    FORMAT_NUMBER(price || ''),
    detailArrival.value.destination_enterprise_name,
    detailArrival.value.destination_user_name,
    detailArrival.value.destination_license_number || '',
    detailArrival.value.destination_user_note_1 || ''
  );
  const href = `tmprintassistant://tmprintassistant.epson.com/print?ver=1&data-type=eposprintxml&data=${dataPrint}`;

  const aTag = document.createElement('a');
  aTag.href = href;
  aTag.click();
  };

if (detailArrival.value.setting?.price_per_quantity) {
  const pricePerQuantity = detailArrival.value.setting?.price_per_quantity || 0;
  const price =
    Number(detailArrival.value.arrival_quantity) * Number(pricePerQuantity);
  const dataPrint = makeQuantityXML(
    detailArrival.value.destination_enterprise_name,
    detailArrival.value.destination_user_name,
    maskCodeString(detailArrival.value.code?.replaceAll('-', '')),
    FORMAT_NUMBER(detailArrival.value?.arrival_quantity),
    // map utc to local time
    dayjs(FORMAT_DATE(detailArrival.value.arrival_date)).toDate(),
    pricePerQuantity || '',
    FORMAT_NUMBER(price || ''),
    detailArrival.value.destination_enterprise_name,
    detailArrival.value.destination_user_name,
    detailArrival.value.destination_license_number || '',
    detailArrival.value.destination_user_note_1 || ''
  );
  const href = `tmprintassistant://tmprintassistant.epson.com/print?ver=1&data-type=eposprintxml&data=${dataPrint}`;

  const aTag = document.createElement('a');
  aTag.href = href;
  aTag.click();
}
};

// push to page edit arrival
const pushToEdit = async () => {
  await router.push({
    name: 'arrivalEdit',
    params: {
      id: arrivalId.value,
    },
  });
};

// confirm cancel arrival
const cancelArrival = async () => {
  isPopupConfirmCancel.value = true;
};
// #endregion function

const goToPage = name => {
  router.push({ name });
};

// #region provide
const popupConfirmTextProvideData = {
  isPopup: isPopupConfirmCancel,
  titlePopup: '取消確認',
  caption: '入荷実績を取り消します。よろしいですか？',
  handleCloseModal: () => {
    isPopupConfirmCancel.value = false;
  },
  handleAcceptModal: async () => {
    const result = await arrivalService.cancelArrival(arrivalId.value);
    isPopupConfirmCancel.value = false;
    if (result.code === 0) {
      toast.access(MESSAGE.MSG_CANCEL_ARRIVAL_INFO);
      await router.push({ name: 'arrivalList' });
    }
  },
};
provide('popupConfirmTextProvideData', popupConfirmTextProvideData);
// #endregion

// #region helpers functions
const maskVolumeTypeToDisplay = settingInfo => {
  if (settingInfo?.price_per_quantity === 0) {
    return INPUT_VOLUME_SHIPMENT_TYPE_OPTIONS[1].label;
  }

  if (settingInfo?.price_per_kilogram === 0) {
    return INPUT_VOLUME_SHIPMENT_TYPE_OPTIONS[0].label;
  }
  return '';
};
  // INPUT_VOLUME_TYPE_OPTIONS.find(item => item.value === value)?.label || '';

// #endregion

onMounted(async () => {
  arrivalId.value = router.currentRoute.value.params?.id;
  const result = await arrivalService.getArrivalDetail(arrivalId.value);
  if (result.code === 401) {
    return;
  }
  if (result.code === 0) {
    detailArrival.value = result.payload;

    // check role staff distribute

    isShowMoreInfo.value =
      result.payload.destination_user?.enterprise_type ===
      ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE;

    isStaff.value =
      result.payload.destination_user?.enterprise_type ===
        ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE &&
      result.payload.destination_user?.staff_type === STAFF_TYPE_ENUM.STAFF &&
      user.value?.enterprise_type ===
        ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE &&
      user.value?.staff_type === STAFF_TYPE_ENUM.ENTERPRISE;

    if (
      !result.payload.qr_code
      && result.payload.shipping_type === SHIPPING_TYPE_ENUM.PROXY
    ) {isPrint.value = true;}
    if (result.payload.can_edit) {
      isEdit.value = true;
    }
    checkCanceFromDate.value = FORMAT_DATE_TIME_UTC(detailArrival.value?.inventory?.cancelable_from_date) < FORMAT_DATE_TIME_UTC(new Date());
  } else {
    router.back();
  }
});
</script>
