// ======== IMPORT ========================
const {
  getInventorySchema,
  getInventoryDetailSchema,
  editInventorySchema,
  resetInventorySchema,
  getInventoryEditedDetailSchema,
  undoChangeInventorySchema,
} = require('./schema');
const { auth, apiKeyVerify, authEnterprise } = require('../base/authorized');
// ===== 1. Injection ==============

// ===== 2. Router Functions ==============
// Get inventory list
async function getInventoryList(request, reply) {
  const inventoryManagementService = request.diScope.resolve('inventoryManagementService');
  const response = await inventoryManagementService.getInventoryList(request.user, request.query);
  return reply.send(response);
}

// Get inventory edited list
async function getInventoryEditedList(request, reply) {
  const inventoryManagementService = request.diScope.resolve('inventoryManagementService');
  const response = await inventoryManagementService.getInventoryEditedList(request.user, request.query);
  return reply.send(response);
}

async function getInventoryDetail(request, reply) {
  const inventoryManagementService = request.diScope.resolve('inventoryManagementService');
  const response = await inventoryManagementService.getInventoryDetail(request.user, request.params.id);
  return reply.send(response);
}

async function getInventoryEditedDetail(request, reply) {
  const inventoryManagementService = request.diScope.resolve('inventoryManagementService');
  const response = await inventoryManagementService.getInventoryEditedDetail(request.user, request.params.id);
  return reply.send(response);
}

async function editInventory(request, reply) {
  const inventoryManagementService = request.diScope.resolve('inventoryManagementService');
  const response = await inventoryManagementService.editInventory(request.user, request.params.id, request.body);
  return reply.send(response);
}

async function resetInventory(request, reply) {
  const inventoryManagementService = request.diScope.resolve('inventoryManagementService');
  const response = await inventoryManagementService.resetInventory(request.user, request.params.id);
  return reply.send(response);
}

async function undoChangeInventory(request, reply) {
  const inventoryManagementService = request.diScope.resolve('inventoryManagementService');
  const response = await inventoryManagementService.undoChangeInventory(request.user, request.params.id);
  return reply.send(response);
}

// ===== 3. Router Registration ============
module.exports = async (fastify) => {
  fastify.get(
    '/list',
    { schema: getInventorySchema, onRequest: [apiKeyVerify, auth, authEnterprise] },
    getInventoryList
  );
  fastify.get(
    '/edited-list',
    { schema: getInventorySchema, onRequest: [apiKeyVerify, auth, authEnterprise] },
    getInventoryEditedList
  );
  fastify.get(
    '/:id',
    { schema: getInventoryDetailSchema, onRequest: [apiKeyVerify, auth, authEnterprise] },
    getInventoryDetail
  );
  fastify.get(
    '/edited/:id',
    { schema: getInventoryEditedDetailSchema, onRequest: [apiKeyVerify, auth, authEnterprise] },
    getInventoryEditedDetail
  );
  fastify.put(
    '/:id',
    { schema: editInventorySchema, onRequest: [apiKeyVerify, auth, authEnterprise] },
    editInventory
  );
  fastify.put(
    '/reset/:id',
    { schema: resetInventorySchema, onRequest: [apiKeyVerify, auth, authEnterprise] },
    resetInventory
  );
  fastify.put(
    '/undo-change/:id',
    { schema: undoChangeInventorySchema, onRequest: [apiKeyVerify, auth, authEnterprise] },
    undoChangeInventory
  );
};
