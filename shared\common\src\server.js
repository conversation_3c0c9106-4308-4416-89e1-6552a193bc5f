'use strict'

// Read the .env file.
if (process.env.NODE_ENV === 'local') {
  require('dotenv').config();
}

// Require the framework
const Fastify = require('fastify');

// Require library to exit fastify process, gracefully (if possible)
const closeWithGrace = require('close-with-grace');

// Instantiate Fastify with some config
const app = Fastify({
  logger: true,
  trustProxy: true
});

app.register(require('@fastify/swagger'), {
  openapi: {
    openapi: '3.0.1',
    info: {
      title: 'SSV Swagger',
      description: 'SSV Swagger Api',
      version: '1.0.0',
      contact: {
        name: "SS<PERSON>",
        email: "<EMAIL>"
      },
    },
    servers: [
      {
        url: 'http://localhost:3000',
        description: 'Development server'
      },
      {
        url: 'https://staging.ssv.com',
        description: 'Staging server'
      },
      {
        url: 'https://ssv.com',
        description: 'Production server'
      },
    ],
    components: {
      securitySchemes: {
        bearToken: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        },
        apiKey: {
          type: 'apiKey',
          in: 'header',
          name: 'x-api-key',
        }
      },
    }
  },
  hideUntagged: true
});

app.register(require('@fastify/swagger-ui'), {
  routePrefix: '/documentation',
  uiConfig: {
    docExpansion: 'list',
    deepLinking: false,
    displayRequestDuration: true,
    displayOperationId: true,
    filter: true,
    syntaxHighlight: true,
    requestSnippetsEnabled: true,
    persistAuthorization: true
  },
  uiHooks: {
    onRequest: function (request, reply, next) { next() },
    preHandler: function (request, reply, next) { next() }
  },
  staticCSP: true,
  transformStaticCSP: (header) => {
    return header;
  },
  transformSpecification: (swaggerObject) => {
    // swaggerObject.servers.unshift({
    //   url: req.hostname,
    //   description: 'Development server'
    // })
    return swaggerObject
  },
  transformSpecificationClone: true
});
// Register your application as a normal plugin.
const appService = require('./app.js');
app.register(appService);

// delay is the number of milliseconds for the graceful close to finish
// eslint-disable-next-line no-unused-vars
const closeListeners = closeWithGrace({ delay: process.env.FASTIFY_CLOSE_GRACE_DELAY || 500 }, async function ({ signal, err }) {
  if (err) {
    app.log.error(err);
  }
  await app.close();
});

app.addHook('onClose', (instance, done) => {
  closeListeners.uninstall()
  done()
});

// Start listening.
app.listen({ port: 3000, host: '0.0.0.0' }, (err) => {
  if (err) {
    app.log.error(err);
    process.exit(1);
  }
});