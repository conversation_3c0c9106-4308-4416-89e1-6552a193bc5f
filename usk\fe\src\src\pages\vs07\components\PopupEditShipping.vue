<template>
  <q-dialog v-model="model" persistent>
    <q-card class="tw:w-[70rem] tw:max-w-[70rem]">
      <div>
        <h2 class="tw:font-bold tw:text-m-design tw:bg-[#004AB9] tw:text-white tw:p-5">
          出荷量変更
        </h2>
        <div class="tw:text-xs-design tw:font-bold tw:p-5">出荷量を入力してください</div>
        <div class="tw:px-5">
          <div
            class="tw:border-2 tw:rounded-[0.5rem] tw:p-3 tw:text-m-design"
            :class="{
              'tw:border-[#004AB9]': inventory?.selectedInfo.checked,
              'tw:border-[#CACACA]': !inventory?.selectedInfo.checked,
            }"
          >
            <div
              class="tw:flex-col tw:flex tw:tl:flex-row tw:tl:gap-4 tw:justify-between tw:font-bold"
            >
              <span class="tw:block tw:truncate">{{ inventory?.groupName }}</span>
              <span
                class="tw:block"
                :class="{
                  'tw:text-[#CACACA]': !inventory?.selectedInfo.checked,
                }"
                >{{ mapNetWeightInventoryToDisplay(inventory) }}</span
              >
            </div>
            <div class="tw:flex-col tw:flex tw:tl:flex-row tw:tl:gap-6">
              <span class="tw:block"
                >最新の入荷日：{{ FORMAT_DATE(inventory?.latestArrivalDate) }}</span
              >
              <span class="tw:block"
                >総在庫量：{{ FORMAT_NUMBER(inventory?.netWeightInventory) }}g</span
              >
            </div>
          </div>

          <div
            class="tw:grid tw:grid-cols-1 tw:tl:grid-cols-2 tw:justify-between tw:gap-2 tw:items-center tw:my-3"
          >
            <div class="tw:flex-1 tw:flex-col tw:gap-2">
              <div class="tw:font-normal tw:text-m-design tw:mb-1 tw:relative">
                全体重量
                <q-badge
                  class="tw:text-white tw:bg-[#E80F00] tw:p-1 tw:rounded font-xxs-design tw:absolute tw:bottom-[0.4rem] tw:dt:bottom-[0.6rem] tw:ml-2"
                >
                  必須
                </q-badge>
              </div>
              <BaseInput
                class="tw:w-full"
                clearable
                outlined
                type="text"
                inputmode="numeric"
                input-class="tw:text-right tw:text-m-design"
                :model-value="form.grossWeight"
                @update:model-value="handleInputGrossWeight"
                :mask="{
                  mask: Number,
                  min: 0,
                  thousandsSeparator: ',',
                  scale: 2,
                  radix: '.',
                  autofix: true,
                  lazy: false,
                }"
                maxlength="9"
                :error="!!errors.grossWeight"
                :error-message="errors.grossWeight"
              >
                <template v-slot:append>
                  <div class="tw:text-m-design tw:text-[#333333] tw:pr-4">g</div>
                </template>
              </BaseInput>
            </div>
            <div class="tw:flex tw:items-center tw:gap-2">
              <svg
                width="20"
                height="68"
                viewBox="0 0 41 68"
                class="tw:text-[#7E8093] tw:mt-10 tw:dt:mt-11"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect x="8.5" y="33" width="31" height="2" fill="#7E8093" />
              </svg>
              <div class="tw:flex-1 tw:flex-col tw:gap-2 tw:tl:items-center tw:ml-1">
                <div class="tw:text-m-design tw:mb-1 tw:tl:min-w-[4rem] tw:dt:min-w-[3rem]">
                  風袋
                </div>
                <BaseInput
                  class="tw:w-full"
                  clearable
                  outlined
                  type="text"
                  inputmode="numeric"
                  autocomplete="nope"
                  input-class="tw:text-right tw:text-m-design"
                  :model-value="form.tareWeight"
                  @update:model-value="handleInputTareWeight"
                  :mask="{
                    mask: Number,
                    min: 0,
                    thousandsSeparator: ',',
                    scale: 2,
                    radix: '.',
                    autofix: true,
                    lazy: false,
                  }"
                  maxlength="9"
                  :error="!!errors.tareWeight && !errors.grossWeight"
                  :error-message="errors.tareWeight"
                >
                  <template v-slot:append>
                    <div class="tw:text-m-design tw:text-[#333333] tw:pr-4">g</div>
                  </template>
                </BaseInput>
              </div>
            </div>
          </div>

          <div class="tw:flex tw:justify-between tw:items-center">
            <div class="tw:text-s-design">出荷量</div>
            <div class="tw:flex tw:font-bold">
              <span class="tw:text-xl-design"
                >{{ form.netWeight }}<span class="tw:text-m-design">g</span></span
              >
            </div>
          </div>
        </div>
        <div class="tw:flex tw:flex-col tw:tl:flex-row tw:justify-end tw:p-5 tw:pt-4 tw:gap-2">
          <BaseButton
            class="tw:flex tw:justify-center tw:items-center tw:mr-4 tw:rounded-full tw:font-bold tw:text-xs-design tw:tl:w-[20rem] tw:w-full"
            :class="`tw:text-[#004AB9]`"
            outline
            label="出荷量入力をやめる"
            @click.prevent="handleClickCancel"
          />
          <BaseButton
            type="button"
            class="tw:flex tw:justify-center tw:items-center tw:text-white tw:font-bold tw:tl:w-[20rem] tw:w-full tw:rounded-full tw:text-xs-design"
            :class="`tw:bg-[#004AB9]`"
            @click.prevent="handleClickSubmit"
            label="出荷量を設定する"
          />
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup>
import BaseButton from 'components/base/vs/BaseButton.vue';
import BaseInput from 'components/base/vs/BaseInput.vue';
import useValidate from 'src/composables/validate';
import { doParseFloatNumber, FORMAT_DATE, FORMAT_NUMBER, isNumeric } from 'src/helpers/common';
import confirmWeightSchema from 'src/schemas/outbound-shipment/confirmWeight.schema';
import { ref, watch } from 'vue';

const model = defineModel();
const emit = defineEmits(['onClickCancel', 'onClickSubmit']);
const { validateData, errors } = useValidate();

const props = defineProps({
  inventory: {
    type: Object,
  },
});
const form = ref({
  grossWeight: '',
  tareWeight: '',
  netWeight: '',
});

// #region functions
const handleInputGrossWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const grossWeightNum = doParseFloatNumber(newValue || 0);
  const tareWeightNum = doParseFloatNumber(form.value.tareWeight || 0);
  const netWeightNum = grossWeightNum - tareWeightNum;
  form.value.grossWeight = newValue;
  form.value.netWeight = netWeightNum >= 0 ? FORMAT_NUMBER(netWeightNum) : undefined;
};

const handleInputTareWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const grossWeightNum = doParseFloatNumber(form.value.grossWeight || 0);
  const tareWeightNum = doParseFloatNumber(newValue || 0);
  const netWeightNum = grossWeightNum - tareWeightNum;
  form.value.tareWeight = newValue;
  form.value.netWeight = netWeightNum >= 0 ? FORMAT_NUMBER(netWeightNum) : undefined;
};

const handleClickCancel = () => {
  emit('onClickCancel');
};

const handleClickSubmit = () => {
  const payload = {
    grossWeight: form.value.grossWeight ? doParseFloatNumber(form.value.grossWeight) : '',
    tareWeight: form.value.tareWeight ? doParseFloatNumber(form.value.tareWeight) : 0,
  };
  const valid = validateData(confirmWeightSchema, payload);
  if (valid) {
    emit('onClickSubmit', payload);
  }
};
// #endregion

// #region helpers
const mapNetWeightInventoryToDisplay = item => {
  if (!item) {
    return '';
  }
  if (item.selectedInfo.checked && item.selectedInfo.netWeight < item.netWeightInventory) {
    return `${item.selectedInfo.netWeight}g`;
  }
  return '在庫全て';
};
// #endregion

watch(
  () => props.inventory,
  () => {
    form.value = {
      grossWeight: '',
      tareWeight: '',
      netWeight: '',
    };
  }
);
</script>

<style scoped>
:deep(a) {
  color: #007bff !important;
}
</style>
