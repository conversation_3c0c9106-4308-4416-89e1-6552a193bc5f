<template>
  <div class="parentPdf tw:font-pdfSans">
    <div id="export-shipment-pdf-file-data">
      <div :style="{ marginLeft: '32px', fontSize: '36px' }">日付・数量のみ表示</div>
      <div class="tw:mt-[64px] tw:space-y-10">
        <table class="page-break-after">
          <thead>
            <tr>
              <th>No.</th>
              <th>漁獲/荷口番号</th>
              <th>事業者名</th>
              <th>日付</th>
              <th>数量[g]</th>
              <th>No.</th>
              <th>関連漁獲/荷口番号<br />又は輸入/養殖である旨</th>
              <th>事業者名</th>
              <th>日付</th>
              <th>数量[g]</th>
              <th>総量[g]</th>
            </tr>
          </thead>
          <tbody>
            <template v-for="(origin, originIndex) in rootOrigin || []" :key="originIndex">
              <tr
                v-for="(inventory, inventoryIndex) in origin.shipping_id_list || []"
                :key="inventory.code"
              >
                <!-- Chỉ hiển thị dữ liệu cha ở dòng đầu tiên -->
                <template v-if="inventoryIndex === 0">
                  <td :rowspan="origin.shipping_id_list.length">A{{ originIndex + 1 }}</td>
                  <td :rowspan="origin.shipping_id_list.length">
                    {{ generateOriginCode(origin.code) }}
                  </td>
                  <td :rowspan="origin.shipping_id_list.length">
                    {{ origin.starting_user_name }} ({{ origin.starting_enterprise_name }})
                  </td>
                  <td :rowspan="origin.shipping_id_list.length">
                    {{ FORMAT_DATE(origin.shipping_date) }}
                  </td>
                  <td :rowspan="origin.shipping_id_list.length">
                    {{ FORMAT_NUMBER(origin.shipping_net_weight) }}
                  </td>
                </template>

                <!-- Inventory info -->
                <td>B{{ inventoryIndex + 1 }}</td>
                <td >
                  {{ generateOriginCode(inventory.code) }}
                </td>
                <td>
                  {{ inventory.starting_user_name }} ({{ inventory.starting_enterprise_name }})
                </td>
                <td>
                  {{ FORMAT_DATE(inventory.shipping_date) }}
                </td>
                <td>
                  {{ FORMAT_NUMBER(inventory.taken_inventory_weight) }}
                </td>

                <!-- Hiển thị tổng lượng ở dòng cuối cùng -->
                <template v-if="inventoryIndex === 0">
                  <td :rowspan="origin.shipping_id_list.length">
                    {{ FORMAT_NUMBER(origin.arrival_net_weight) }}
                  </td>
                </template>
              </tr>
            </template>
          </tbody>
        </table>

        <table v-if="distributionOrigins.length" class="page-break-after">
          <thead>
            <tr>
              <th>No.</th>
              <th>漁獲/荷口番号</th>
              <th>事業者名</th>
              <th>日付</th>
              <th>数量[g]</th>
              <th>No.</th>
              <th>関連漁獲/荷口番号<br />又は輸入/養殖である旨</th>
              <th>事業者名</th>
              <th>日付</th>
              <th>数量[g]</th>
              <th>総量[g]</th>
            </tr>
          </thead>
          <tbody>
            <template v-for="(origin, originIndex) in distributionOrigins || []" :key="originIndex">
              <tr
                v-for="(inventory, inventoryIndex) in origin.shipping_id_list || []"
                :key="inventory.code"
              >
                <!-- Chỉ hiển thị dữ liệu cha ở dòng đầu tiên -->
                <template v-if="inventoryIndex === 0">
                  <td :rowspan="origin.shipping_id_list.length">B{{ originIndex + 1 }}</td>
                  <td :rowspan="origin.shipping_id_list.length">
                    {{ generateOriginCode(origin.code) }}
                  </td>
                  <td :rowspan="origin.shipping_id_list.length">
                    {{ origin.starting_user_name }} ({{ origin.starting_enterprise_name }})
                  </td>
                  <td :rowspan="origin.shipping_id_list.length">
                    {{ FORMAT_DATE(origin.shipping_date) }}
                  </td>
                  <td :rowspan="origin.shipping_id_list.length">
                    {{ FORMAT_NUMBER(origin.shipping_net_weight) }}
                  </td>
                </template>

                <!-- Inventory info -->
                <td>C{{ inventoryIndex + 1 }}</td>
                <td >
                  {{ generateOriginCode(inventory.code) }}
                </td>
                <td>
                  {{ inventory.starting_user_name }} ({{ inventory.starting_enterprise_name }})
                </td>
                <td>
                  {{ FORMAT_DATE(inventory.shipping_date) }}
                </td>
                <td>
                  {{ FORMAT_NUMBER(inventory.taken_inventory_weight) }}
                </td>

                <!-- Hiển thị tổng lượng ở dòng cuối cùng -->
                <template v-if="inventoryIndex === 0">
                  <td :rowspan="origin.shipping_id_list.length">
                    {{ FORMAT_NUMBER(origin.arrival_net_weight) }}
                  </td>
                </template>
              </tr>
            </template>
          </tbody>
        </table>

        <table v-if="catchingOrigins.length" class="page-break-after">
          <thead>
            <tr>
              <th>No.</th>
              <th>漁獲/荷口番号</th>
              <th>事業者名</th>
              <th>日付</th>
              <th>数量[g]</th>
              <th>No.</th>
              <th>関連漁獲/荷口番号<br />又は輸入/養殖である旨</th>
              <th>事業者名</th>
              <th>日付</th>
              <th>数量[g]</th>
              <th>総量[g]</th>
            </tr>
          </thead>
          <tbody>
            <template v-for="(origin, originIndex) in distributionOrigins || []" :key="originIndex">
              <tr
                v-for="(inventory, inventoryIndex) in origin.inventory_info_codes || []"
                :key="inventory.code"
              >
                <!-- Chỉ hiển thị dữ liệu cha ở dòng đầu tiên -->
                <template v-if="inventoryIndex === 0">
                  <td :rowspan="origin.inventory_info_codes.length">A01</td>
                  <td :rowspan="origin.inventory_info_codes.length">
                    {{ generateOriginCode(origin.code) }}
                  </td>
                  <td :rowspan="origin.inventory_info_codes.length">
                    {{ origin.starting_user_name }} ({{ origin.starting_enterprise_name }})
                  </td>
                  <td :rowspan="origin.inventory_info_codes.length">
                    {{ FORMAT_DATE(origin.shipping_date) }}
                  </td>
                  <td :rowspan="origin.inventory_info_codes.length">
                    {{ FORMAT_NUMBER(origin.shipping_net_weight) }}
                  </td>
                </template>

                <!-- Inventory info -->
                <td>A01</td>
                <td >
                  {{ generateOriginCode(inventory.code) }}
                </td>
                <td>
                  {{ inventory.starting_user_name }} ({{ inventory.starting_enterprise_name }})
                </td>
                <td>
                  {{ FORMAT_DATE(inventory.shipping_date) }}
                </td>
                <td>
                  {{ FORMAT_NUMBER(inventory.shipping_net_weight) }}
                </td>

                <!-- Hiển thị tổng lượng ở dòng cuối cùng -->
                <template v-if="inventoryIndex === 0">
                  <td :rowspan="origin.inventory_info_codes.length">
                    {{ FORMAT_NUMBER(origin.arrival_net_weight) }}
                  </td>
                </template>
              </tr>
            </template>
          </tbody>
        </table>

      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, inject } from 'vue';
import { FORMAT_DATE, FORMAT_NUMBER } from 'helpers/common';
import { maskCodeString } from 'src/helpers/common';

const data = inject('exportShipmentPDFProvideData');

const rootOrigin = computed(() => data.value.rootOrigins);

const distributionOrigins = computed(() => data.value.distributionOrigins);

const catchingOrigins = computed(() => {
  if (data.value.catchingOrigins?.length) {
    return data.value.catchingOrigins;
  }
  return [];
});

const generateOriginCode = code => {
  if (!code) {
    return '';
  }
  return maskCodeString(code);
};

// const generateReasonDiffWeight = typeDiff => {
//   if (!typeDiff) {
//     return '';
//   }
//   switch (typeDiff) {
//     case TYPE_DIFFERENCE_WEIGHT_ENUM.DEATH:
//       return '斃死';
//     case TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR:
//       return '計量誤差';
//     default:
//       return 'その他';
//   }
// };
</script>

<style scoped>
.parentPdf {
  display: none !important;
  font-size: 100% !important;
}

table {
  width: 100%;
  border-collapse: collapse;
}

.page-break-after {
  page-break-after: always;
}

tr {
  page-break-inside: avoid;
}

th,
td {
  border: 1px solid #000;
  padding-left: 8px !important;
  padding-right: 8px !important;
  padding-top: 0px;
  padding-bottom: 14px !important;
  font-weight: 400;
  vertical-align: middle;
  height: 40px;
  font-size: 14px !important;
}

th {
  background-color: #fff;
  text-align: 'center';
}
</style>
