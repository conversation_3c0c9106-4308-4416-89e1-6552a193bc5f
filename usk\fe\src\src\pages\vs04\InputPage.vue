<template>
  <q-card
    class="tw:p-5 tw:flex tw:flex-col tw:h-full tw:pb-[19rem] tw:tl:pb-[8rem]"
  >
    <div class="tw:text-l-design tw:font-bold">取引先新規登録</div>
    <div class="tw:text-m-design tw:mt-5">
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-[#E0E0E0]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 事業者区分 </span>
          <q-badge
            class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
          >
            必須
          </q-badge>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
        >
          <q-field
            :error="!!errors.enterpriseType"
            no-error-icon
            borderless
            hide-bottom-space
          >
            <template v-slot:control>
              <div class="tw:flex tw:w-full tw:gap-x-3">
                <q-radio
                  v-for="(item, index) in enterpriseTypeOptions"
                  v-model="form.enterpriseType"
                  :key="index"
                  :val="item.value"
                  :label="item.label"
                  size="lg"
                  class="tw:text-m-design"
                />
              </div>
            </template>
            <template v-slot:error>
              <div class="tw:pl-3 -tw:translate-y-3">
                {{ errors.enterpriseType }}
              </div>
            </template>
          </q-field>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 都道府県 </span>
          <q-badge
            class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
          >
            必須
          </q-badge>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-2 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
        >
          <BaseSingleSelectInput
            v-model="form.province"
            class="tw:w-full"
            :class="!!errors.province ? 'tw:mb-11 tw:tl:mb-10 tw:xl:mb-8' : ''"
            :options="optionsRegions"
            :error="!!errors.province"
            :error-message="errors.province"
            :use-input="false"
            :clearable="false"
            emit-value
            map-options
          >
            <template v-slot:option="scope">
              <div>
                <div
                  class="text-weight-bold text-black q-pl-sm q-pt-xs q-pb-xs"
                  style="pointer-events: none;"
                >
                  {{ scope.props?.opt?.label }}
                </div>

                <div v-for="child in scope.props?.opt?.children" :key="child.value">
                  <q-item
                    dense
                    clickable
                    v-ripple
                    v-close-popup
                    @click="form.province = child.value"
                    :class="{ 'bg-light-blue-1': form.province === child.value }"
                  >
                    <q-item-section>
                      <q-item-label>{{ child.label }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </div>
              </div>
            </template>

            <template v-slot:selected-item>
              <div v-if="selectedProvince" class="tw:p-0">
                  <span class="tw:text-m-design">{{ selectedProvince.label }}</span>
              </div>
            </template>
          </BaseSingleSelectInput>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 届出番号 </span>
          <q-badge
            class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
          >
            必須
          </q-badge>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:min-h-[4.25rem]
          tw:py-2 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
        >
          <BaseInput
            v-model.trim="form.enterpriseCode"
            input-class="tw:text-[#333333] tw:text-m-design tw:pb-1"
            :class="!!errors.enterpriseCode ? 'tw:mb-11 tw:tl:mb-10 tw:xl:mb-8' : ''"
            outlined
            maxlength="7"
            inputmode="numeric"
            autocomplete="nope"
            :error="!!errors.enterpriseCode"
            :error-message="errors.enterpriseCode"
            :mask="{
              // only allow 7 digits numbers
              mask: /^\d{0,7}$/,
            }"
            class="tw:w-full"
          />
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 事業者名 </span>
          <q-badge
            class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
          >
            必須
          </q-badge>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-2 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
        >
          <BaseInput
            v-model.trim="form.enterpriseName"
            class="tw:w-full"
            input-class="tw:text-[#333333] tw:text-m-design tw:pb-1"
            :class="!!errors.enterpriseName ? 'tw:mb-11 tw:tl:mb-10 tw:xl:mb-8' : ''"
            outlined
            maxlength="50"
            autocomplete="nope"
            :error="!!errors.enterpriseName"
            :error-message="errors.enterpriseName"
          />
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 事業者名（カナ）</span>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-2 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
        >
          <BaseInput
            v-model.trim="form.enterpriseNameKana"
            hide-bottom-space
            input-class="tw:text-[#333333] tw:text-m-design tw:pb-1"
            :class="!!errors.enterpriseNameKana ? 'tw:mb-11 tw:tl:mb-10 tw:xl:mb-8' : ''"
            outlined
            maxlength="50"
            autocomplete="nope"
            class="tw:w-full"
            :error="!!errors.enterpriseNameKana"
            :error-message="errors.enterpriseNameKana"
            no-error-icon
          />
        </div>
      </div>
      <div
        class="ttw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 取引先区分 </span>
          <q-badge
            class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
          >
            必須
          </q-badge>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
          :class="!!errors.partnerType ? 'tw:pb-0' : ''"
        >
          <q-field
            :error="!!errors.partnerType"
            no-error-icon
            borderless
            hide-bottom-space
          >
            <template v-slot:control>
              <div class="tw:flex tw:w-full tw:gap-x-3">
                <q-checkbox
                  class="tw:text-m-design"
                  v-for="(item, index) in partnerTypeOptions"
                  :key="index"
                  v-model="form.partnerType"
                  :label="item.label"
                  :val="item.value"
                  size="lg"
                />
              </div>
            </template>
            <template v-slot:error>
              <div class="tw:pl-6 tw:xl:pl-4 tw:transform tw:-translate-y-4">
                {{ errors.partnerType }}
              </div>
            </template>
          </q-field>
        </div>
      </div>
    </div>

    <q-footer
      elevated
      class="tw:bg-white tw:p-4 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
      tw:tl:justify-between tw:flex tw:justify-center tw:mt-4 tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
        tw:tl:w-[21.15rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
        tw:w-full`"
        label="取引先登録に戻る"
        @click.prevent="router.push({ name: 'partner' })"
      />
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-blue-3 tw:text-white tw:text-m-design tw:tl:font-bold
        tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
        tw:w-full`"
        label="確認する"
        @click.prevent="handleClickConfirm"
      />
    </q-footer>
  </q-card>
</template>
<script setup>
import useValidate from 'composables/validate';
import { PARTNER_TYPE_ENUM } from 'helpers/constants';
import { storeToRefs } from 'pinia';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import BaseInput from 'src/components/base/vs/BaseInput.vue';
import BaseSingleSelectInput from 'src/components/base/vs/BaseSingleSelectInput.vue';
import { ENTERPRISE_TYPE_ENUM } from 'src/helpers/constants';
import MESSAGE from 'src/helpers/message';
import registerPartnerSchema from 'src/schemas/partner/registerPartner.schema';
import regionsService from 'src/shared/services/regions.service';
import { useAppStore } from 'src/stores/app-store';
import { useAuthStore } from 'src/stores/auth-store';
import { useConfirmFormStore } from 'stores/confirm-form-store';
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import toast from 'src/shared/utilities/toast';

// #region variable
const { validateData, errors } = useValidate();
const router = useRouter();
const optionsRegions = ref([]);
const { setConfirmData, getConfirmData } = useConfirmFormStore();
const { previousRoute } = storeToRefs(useAppStore());
const { user } = storeToRefs(useAuthStore());

const form = ref({
  enterpriseType: ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE.toString(),
  province: '',
  enterpriseCode: '',
  enterpriseName: '',
  enterpriseNameKana: '',
  partnerType: [],
});

const partnerTypeOptions = [
  {
    label: '仕入先',
    value: PARTNER_TYPE_ENUM.SUPPLIER.toString(),
  },
  {
    label: '出荷先',
    value: PARTNER_TYPE_ENUM.SHIPPER.toString(),
  },
];

const enterpriseTypeOptions = [
  {
    label: '採捕事業者',
    value: ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE.toString(),
  },
  {
    label: '取扱事業者',
    value: ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE.toString(),
  },
];
// #endregion variable

// #region function
const selectedProvince = computed(() => {
  const provinceId = form.value.province;
  for (const region of optionsRegions.value) {
    const found = region.children.find(p => p.value === provinceId);
    if (found) {return found;}
  }
  return null;
});

const handleClickConfirm = async () => {
  const payload = {
    ...form.value,
    enterpriseType: form.value.enterpriseType ? parseInt(form.value.enterpriseType) : '',
    partnerType: form.value.partnerType.map(Number),
    enterpriseNameKana: form.value.enterpriseNameKana || undefined,
  };

  const isValid = validateData(registerPartnerSchema, payload);

  if (isValid) {
    // validate enterpriseType and enterpriseCode
    // if enterpriseType is Catch Enterprise then enterpriseCode must be start with "0"
    if (form.value.enterpriseType === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE.toString() &&
        !form.value.enterpriseCode.startsWith('0')) {
      errors.value.enterpriseCode = MESSAGE.MSG_INVARID_NOTIFICATIONUMBER_SAIHO_ERROR;
      return;
    }
    // if enterpriseType is Distribute Enterprise then enterpriseCode must be start with "5"
    if (form.value.enterpriseType === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE.toString() &&
        !form.value.enterpriseCode.startsWith('5')) {
      errors.value.enterpriseCode = MESSAGE.MSG_INVARID_NOTIFICATIONUMBER_DISTRIBUTOR_ERROR;
      return;
    }

    // if user is catch enterprise and form is also catch enterprise
    // then show error message
    if (
      user.value.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
      form.value.enterpriseType === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE.toString()
    ) {
      errors.value.enterpriseType = MESSAGE.MSG_DIFFERENT_NOTIFICATIONUMBER_PARTNER_ERROR;
      return;
    }

    setConfirmData({
      ...form.value,
      provinceName: selectedProvince.value.label || '',
    });
    router.push({ name: 'partnerConfirm' });
  }
};
// #endregion function

onMounted(async () => {
  const listRegions = await regionsService.getRegionWithProvinces();
  optionsRegions.value = listRegions.payload.items.map(item => ({
    label: item.region_name,
    children: item.province.map(province => ({
      label: province.name,
      value: province.id,
    })),
  }));

  const newPartnerStoreData = getConfirmData();
  if (
    newPartnerStoreData &&
    previousRoute.value.name === 'partnerConfirm'
  ) {
    const {provinceName, ...rest} = newPartnerStoreData;
    form.value = {
      ...rest,
    };
  }
});
</script>
