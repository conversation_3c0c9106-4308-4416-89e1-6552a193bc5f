import BaseService from 'services/base.service';

class InventoryManagementService extends BaseService {
  async getInventoryList(query) {
    try {
      return this.dao.getInventoryList(query);
    } catch (error) {
      return null;
    }
  }

  async getInventoryEditedList(query) {
    try {
      return this.dao.getInventoryEditedList(query);
    } catch (error) {
      return null;
    }
  }

  async getInventoryDetail(id) {
    try {
      return this.dao.getInventoryDetail(id);
    } catch (error) {
      return null;
    }
  }

  async getInventoryEditedDetail(id) {
    try {
      return this.dao.getInventoryEditedDetail(id);
    } catch (error) {
      return null;
    }
  }

  async editInventory(id, data) {
    try {
      return this.dao.editInventory(id, data);
    } catch (error) {
      return null;
    }
  }

  async undoChangeInventory(id) {
    try {
      return this.dao.undoChangeInventory(id);
    } catch (error) {
      return null;
    }
  }

  async resetInventory(id) {
    try {
      return this.dao.resetInventory(id);
    } catch (error) {
      return null;
    }
  }
}

export default new InventoryManagementService('inventory-management');
