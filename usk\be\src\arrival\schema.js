const { sharedSchema } = require('../validations/sharedSchema');
const { MESSAGE } = require('../utils/message');
const { TYPE_DIFFERENCE_WEIGHT_ENUM, UNIT_TYPE_SETTING_ENUM } = require('../helpers/enum');

const getQrArrivalSchema = {
  summary: 'Get Qr Import',
  description: 'Get Qr Import',
  tags: ['Arrival Feature'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  query: {
    additionalProperties: false,
    type: 'object',
    required: ['qrCode'],
    properties: {
      qrCode: {
        type: 'string',
        pattern: '^[A-Za-z0-9]{16}$',
        errorMessage: {
          _: MESSAGE.QR_CODE_INVALID,
        },
      },
    },
  },
};

const registerManualSchema = {
  summary: 'Register manual arrival',
  description: 'Register manual arrival',
  tags: ['Arrival Feature'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  body: {
    type: 'object',
    additionalProperties: false,
    required: [
      'code',
      'date',
      'volume_type',
      'gross_weight',
      'tare_weight',
      'quantity',
      'code_suffix_id',
      'supplier',
      'setting',
    ],
    properties: {
      code: {
        type: 'string',
        minLength: 16,
        maxLength: 16,
        errorMessage: {
          minLength: MESSAGE.MSG_INVALID_OUTBOUND_CODE_ERROR,
          maxLength: MESSAGE.MSG_INVALID_OUTBOUND_CODE_ERROR,
        },
      },
      supplier: {
        type: 'number',
        minimum: 1,
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      date: {
        type: 'string',
        format: 'slash-date',
        minLength: 1,
        errorMessage: {
          format: MESSAGE.MSG_LIMITS_DATE_ERROR,
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      volume_type: {
        type: 'integer',
        enum: Object.values(UNIT_TYPE_SETTING_ENUM),
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      gross_weight: {
        type: 'number',
        exclusiveMinimum: 0,
        checkFormatDecimal: true,
        errorMessage: {
          checkFormatDecimal: MESSAGE.MSG_INVALID_DECIMAL_ERROR,
          exclusiveMinimum: MESSAGE.MSG_LIMITS_GROSSWEIGHT_MIN_ERROR,
        },
      },
      tare_weight: {
        type: 'number',
        minimum: 0,
        maximum: {
          $data: '1/grossWeight',
        },
        checkFormatDecimal: true,
        errorMessage: {
          checkFormatDecimal: MESSAGE.MSG_INVALID_DECIMAL_ERROR,
          minimum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MIN_ERROR,
          maximum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MAX_ERROR,
        },
      },
      quantity: {
        type: 'integer',
        minimum: 1,
        errorMessage: {
          type: MESSAGE.MSG_INVALID_INTEGER_ERROR,
          minimum: MESSAGE.MSG_LIMITS_QUANTITY_MIN_ERROR,
        },
      },
      code_suffix_id: {
        type: 'number',
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      setting: {
        type: 'object',
        required: ['display_shipment_weight'],
        properties: {
          display_shipment_weight: {
            type: 'boolean',
          },
        },
        errorMessage: {
          required: {
            display_shipment_weight: MESSAGE.MSG_REQUIRED_DISPLAYSHIPMENTWEIGHT_ERROR,
          },
        },
        additionalProperties: false,
      },
    },
  },
};

const registerByQrSchema = {
  summary: 'Register arrival by qrCode',
  description: 'Register arrival by qrCode',
  tags: ['Arrival Feature'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  body: {
    type: 'object',
    additionalProperties: false,
    required: [
      'code',
      'date',
      'gross_weight',
      'tare_weight',
      'supplier',
    ],
    properties: {
      code: {
        type: 'string',
        minLength: 16,
        maxLength: 16,
        errorMessage: {
          minLength: MESSAGE.MSG_INVALID_OUTBOUND_CODE_ERROR,
          maxLength: MESSAGE.MSG_INVALID_OUTBOUND_CODE_ERROR,
        },
      },
      supplier: {
        type: 'number',
        minimum: 1,
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      date: {
        type: 'string',
        format: 'slash-date',
        minLength: 1,
        errorMessage: {
          format: MESSAGE.MSG_LIMITS_DATE_ERROR,
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      gross_weight: {
        type: 'number',
        exclusiveMinimum: 0,
        checkFormatDecimal: true,
        errorMessage: {
          checkFormatDecimal: MESSAGE.MSG_INVALID_DECIMAL_ERROR,
          exclusiveMinimum: MESSAGE.MSG_LIMITS_GROSSWEIGHT_MIN_ERROR,
        },
      },
      tare_weight: {
        type: 'number',
        minimum: 0,
        maximum: {
          $data: '1/grossWeight',
        },
        checkFormatDecimal: true,
        errorMessage: {
          checkFormatDecimal: MESSAGE.MSG_INVALID_DECIMAL_ERROR,
          minimum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MIN_ERROR,
          maximum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MAX_ERROR,
        },
      },
      type_diff: {
        type: 'number',
        enum: Object.values(TYPE_DIFFERENCE_WEIGHT_ENUM),
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      reason_diff: {
        type: 'string',
        minLength: 1,
        maxLength: 300,
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
    },
  },
};

const getArrivalListSchema = {
  summary: 'Get arrival list',
  description: 'Get arrival list',
  tags: ['Arrival Feature'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  query: {
    additionalProperties: false,
    type: 'object',
    properties: {
      page: {
        type: 'integer',
        errorMessage: {
          _: MESSAGE.MSG_PAGE_INDEX_INVALID,
        },
      },
      limit: {
        type: 'integer',
        errorMessage: {
          _: MESSAGE.MSG_PAGE_SIZE_INVALID,
        },
      },
      licenseNumber: {
        type: 'string',
        maxLength: 256,
        errorMessage: {
          _: MESSAGE.LICENSE_NUMBER_ERROR,
        },
      },
      supplier: {
        type: 'string',
      },
      enterpriseName: {
        type: 'string',
      },
      enterpriseCode: {
        type: 'string',
        pattern: '^\\d+$',
        errorMessage: {
          format: MESSAGE.MSG_LIMITS_NUMBER_ERROR,
        },
      },
      startArrivalDate: {
        type: 'string',
        format: 'slash-date',
        errorMessage: {
          format: MESSAGE.START_ARRIVAL_DATE_ERROR,
        },
      },
      endArrivalDate: {
        type: 'string',
        format: 'slash-date',
        formatMinimum: {
          $data: '1/startArrivalDate',
        },
        errorMessage: {
          format: MESSAGE.END_ARRIVAL_DATE_ERROR,
          formatMinimum: MESSAGE.MSG_START_DATE_CONSTRAINT_ERROR,
        },
      },
      sortBy: {
        type: 'string',
        enum: ['id', 'arrival_net_weight', 'starting_enterprise_name', 'starting_user_name'],
        errorMessage: {
          _: MESSAGE.SORT_BY_ERROR,
        },
      },
      note1: {
        type: 'string',
        maxLength: 256,
      },
      note2: {
        type: 'string',
        maxLength: 256,
      },
    },
  },
};

const getArrivalDetailSchema = {
  summary: 'Get arrival detail',
  description: 'Get arrival detail',
  tags: ['Arrival Feature'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
};

const editArrivalSchema = {
  summary: 'Edit arrival',
  description: 'Edit arrival',
  tags: ['Arrival Feature'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  body: {
    type: 'object',
    additionalProperties: false,
    required: ['arrivalDate', 'arrivalGrossWeight'],
    properties: {
      arrivalDate: {
        type: 'string',
        format: 'slash-date',
        minLength: 1,
        errorMessage: {
          format: MESSAGE.MSG_LIMITS_DATE_ERROR,
          _: MESSAGE.MSG_ARRIVAL_DATE_ERROR,
        },
      },
      arrivalGrossWeight: {
        type: 'number',
        exclusiveMinimum: 0,
        exclusiveMaximum: 1000000,
        errorMessage: {
          exclusiveMinimum: MESSAGE.MSG_ARRIVAL_GROSS_WEIGHT_MIN_ERROR,
          _: MESSAGE.MSG_ARRIVAL_GROSS_WEIGHT_ERROR,
        },
      },
      arrivalTareWeight: {
        type: 'number',
        minimum: 0,
        exclusiveMaximum: {
          $data: '1/arrivalGrossWeight',
        },
        errorMessage: {
          minimum: MESSAGE.MSG_ARRIVAL_TARE_WEIGHT_MIN_ERROR,
          exclusiveMaximum: MESSAGE.MSG_ARRIVAL_TARE_WEIGHT_MAX_ERROR,
          _: MESSAGE.MSG_ARRIVAL_TARE_WEIGHT_ERROR,
        },
      },
      arrivalQuantity: {
        type: 'number',
        minimum: 1,
        errorMessage: {
          minimum: MESSAGE.MSG_ARRIVAL_QUANTITY_MIN_ERROR,
          _: MESSAGE.MSG_ARRIVAL_QUANTITY_ERROR,
        },
      },
      typeDiff: {
        type: 'number',
        enum: [
          TYPE_DIFFERENCE_WEIGHT_ENUM.DEATH,
          TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR,
          TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER,
        ],
        errorMessage: {
          _: MESSAGE.TYPE_DIFFERENCE_REQUIRED,
        },
      },
      reasonDiff: {
        type: 'string',
        minLength: 1,
        maxLength: 300,
        errorMessage: {
          minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
          _: MESSAGE.REASON_DIFFERENCE_INVALID,
        },
      },
    },
  },
};

const cancelArrivalSchema = {
  summary: 'Cancel arrival detail',
  description: 'Cancel arrival detail',
  tags: ['Arrival Feature'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
};

module.exports = {
  getQrArrivalSchema,
  registerManualSchema,
  registerByQrSchema,
  getArrivalListSchema,
  getArrivalDetailSchema,
  editArrivalSchema,
  cancelArrivalSchema,
};
