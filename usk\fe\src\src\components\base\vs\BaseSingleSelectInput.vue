<template>
  <q-select
    :class="[$attrs['class'] ?? 'tw:text-l-design', 'tw:h-[4.4rem] vs icon']"
    use-input
    ref="inputRef"
    outlined
    input-debounce="0"
    no-error-icon
    clearable
    hide-dropdown-icon
    hide-bottom-space
    hide-selected
    :input-class="[$attrs['input-class'] ?? 'tw:text-l-design', 'tw:min-h-full']"
    popup-content-class ="tw:max-w-full tw:tl:max-w-[40rem]"

  >
    <template v-slot:no-option>
      <q-item>
        <q-item-section class="text-grey">データが見つかりません。</q-item-section>
      </q-item>
    </template>
    <template v-slot:append>
      <q-icon name="expand_more" class="tw:bg-[#004AB9] tw:justify-start tw:flex" color="white" :size="computedFontSize" />
    </template>
    <template v-for="(_, name) in $slots" v-slot:[name]="scope" :key="name">
      <slot :name="name" :props="scope" />
    </template>
  </q-select>
</template>
<script setup>
import { ref, onMounted } from 'vue';
const inputRef = ref(null);
const computedFontSize = ref('16px');

onMounted(() => {
  const el = inputRef.value?.$el?.querySelector('.q-field__control');

  if (el) {
    const px = parseFloat(getComputedStyle(el).height);
    const rootFontSize = parseFloat(getComputedStyle(document.documentElement).fontSize);
    computedFontSize.value = `${px/rootFontSize}rem`;
  }
});
</script>
