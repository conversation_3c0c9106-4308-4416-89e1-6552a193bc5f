const { ControlledException } = require("../base/errors");
const BaseService = require("../base/serviceFn");
const { MESSAGE } = require("../utils/message");
const { Prisma } = require("@prisma/client");
const dayjs = require("../boot/dayjs");
const {
  PARTNER_TYPE_ENUM,
  ENTERPRISE_TYPE_ENUM,
  ROLES_ENUM,
  USER_STATUS_ENUM,
  SHOW_DEFAULT_SCAN_QR_ENUM,
  STAFF_TYPE_ENUM,
  PAGINATION,
  TOTAL_LIMIT_EXPORT,
} = require("../helpers/enum");
const ShortUniqueId = require("short-unique-id");
class LocatedTransaction extends BaseService {
  #generateSecurePassword(length = 8) {
    const lowerCase = "abcdefghijkmnopqrstuvwxyz";
    const upperCase = "ABCDEFGHIJKLMNPQRSTUVWXYZ";
    const numbers = "23456789";
    const allChars = lowerCase + upperCase + numbers;

    const password = [
      lowerCase[Math.floor(Math.random() * lowerCase.length)],
      upperCase[Math.floor(Math.random() * upperCase.length)],
      numbers[Math.floor(Math.random() * numbers.length)],
    ];

    while (password.length < length) {
      const randomChar = allChars[Math.floor(Math.random() * allChars.length)];
      password.push(randomChar);
    }

    return password.sort(() => Math.random() - 0.5).join("");
  }

  // eslint-disable-next-line no-unused-private-class-members
  #validatePartnerType(partnerType, user, partner) {
    switch (user.enterprise_type) {
      case ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE:
        // if partner type has supplier return error
        // because catching has only one relationship type (shipper)
        // if my enterprise is catching and partner is catching return error
        // because catching has not relationship with catching
        // Other cases are valid
        if (
          partner.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE ||
          partnerType.includes(PARTNER_TYPE_ENUM.SUPPLIER)
        ) {
          throw new ControlledException(MESSAGE.MSG_PARTNER_NOT_MATCH_FUNCTION);
        }
        break;
      case ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE:
        // if partner is catching and partner type has shipper return error
        if (
          partner.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
          partnerType.includes(PARTNER_TYPE_ENUM.SHIPPER)
        ) {
          throw new ControlledException(MESSAGE.MSG_PARTNER_NOT_MATCH_FUNCTION);
        }
        // if partner is eel farming and partner type has supplier return error
        if (
          partner.enterprise_type ===
          ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE &&
          partnerType.includes(PARTNER_TYPE_ENUM.SUPPLIER)
        ) {
          throw new ControlledException(MESSAGE.MSG_PARTNER_NOT_MATCH_FUNCTION);
        }
        // Other cases are valid
        break;
      case ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE:
        // if partner type has shipper return error
        // because eel farming has only one relationship type (supplier)
        // if my enterprise is eel farming and partner is eel farming return error
        // because eel farming has not relationship with eel farming
        // Other cases are valid
        if (
          partner.enterprise_type ===
          ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE ||
          partnerType.includes(PARTNER_TYPE_ENUM.SHIPPER)
        ) {
          throw new ControlledException(MESSAGE.MSG_PARTNER_NOT_MATCH_FUNCTION);
        }
        break;
      default:
        break;
    }
  }

  async searchAll(params) {
    const {
      page,
      limit,
      name,
      partnerType,
      hiddenFlag,
      code,
      userId,
      enterpriseName,
      licenseNumber,
    } = params;
    const enterpriseNameNospace = enterpriseName?.replace(/\s+/g, "");
    const nameNospace = name?.replace(/\s+/g, "");
    const licenseNumberNospace = licenseNumber?.replace(/\s+/g, "");
    const connect = this.DB.READ;

    const searchCondition = {
      AND: [
        {
          OR: [
            {
              partner: {
                enterprise: {
                  enterprise_name_nospace: {
                    contains: enterpriseNameNospace || "",
                  },
                },
              },
            },
            {
              partner: {
                enterprise: {
                  enterprise_name_kana_nospace: {
                    contains: enterpriseNameNospace || "",
                  },
                },
              },
            },
          ],
        },
        {
          user_id: userId,
          delete_flag: false,
          hidden_flag: hiddenFlag ? undefined : false,
          partner_type: {
            // if partnerType is empty, return all
            hasEvery: partnerType ? [partnerType] : [],
          },
          partner: {
            enterprise: {
              enterprise_code: code
                ? {
                  contains: code,
                }
                : undefined,
            },
            status: {
              not: USER_STATUS_ENUM.PENDING,
            },
          },
        },
        {
          OR: [
            {
              partner: {
                license_number: licenseNumberNospace
                  ? {
                    contains: licenseNumberNospace,
                  }
                  : undefined,
              },
            },
            {
              partner: {
                license_number: licenseNumber
                  ? {
                    contains: licenseNumber,
                  }
                  : undefined,
              },
            },
          ],
        },
        {
          OR: [
            {
              partner: {
                name_nospace: nameNospace
                  ? {
                    contains: nameNospace,
                  }
                  : undefined,
              },
            },
            {
              partner: {
                name_kana_nospace: nameNospace
                  ? {
                    contains: nameNospace,
                  }
                  : undefined,
              },
            },
          ],
        },
      ],
    };

    const query = {
      select: {
        id: true,
        partner_type: true,
        hidden_flag: true,
        partner: {
          select: {
            name_nospace: true,
            enterprise: true,
            enterprise_type: true,
            staff_type: true,
            role: true,
            name: true,
          },
        },
      },
      where: searchCondition,
    };
    const count = await connect.partners.count({
      where: searchCondition,
    });

    if (count > TOTAL_LIMIT_EXPORT) {
      throw new ControlledException(MESSAGE.MSG_TOO_MANY_RESULTS);
    }

    if (count === 0) {
      return this.SUCCESS({
        total_item: count,
        items: [],
        page: 1,
        page_size: +limit,
      });
    }

    const offset = (+page - 1) * +limit;
    let tempPage = +page;

    // if offset is greater than total item, recalculate page and offset
    if (offset >= count) {
      tempPage = Math.ceil(count / +limit);
    }

    const data = await connect.partners.findMany({
      ...query,
      orderBy: { created_on: "desc" },
    });

    return this.SUCCESS({
      items: data,
      page: tempPage,
      page_size: +limit,
      total_item: count,
    });
  }

  /**
   * get partner list for hybrid search
   * @param {*} user user login information
   * @param {*} queries queries from request
   * @returns partner list
   */
  async getPartnerHybridListHandler(user, queries) {
    const connect = this.DB.READ;

    // get queries
    const {
      page = PAGINATION.FIRST_PAGE,
      rowsPerPage = PAGINATION.PAGE_SIZE,
      type,
      name,
      userCode,
      licenseNumber,
    } = queries;

    // get current user partners
    const partner = await connect.partners.findMany({
      where: {
        user_id: user.id,
        delete_flag: false,
      },
      select: {
        partner_id: true,
      },
    });
    const partnerIds = partner.map((p) => p.partner_id);

    const searchCondition = {
      // with name query, search by name or name_kana (search contains)
      OR: [
        {
          name: {
            contains: name ?? "",
          },
        },
        {
          name_kana: {
            contains: name ?? "",
          },
        },
      ],
      // with userCode query, search by user_code (search exact)
      user_code: userCode ?? undefined,
      // with licenseNumber query, search by license_number (search exact)
      license_number: licenseNumber ?? undefined,
      // with type query, search by enterprise_type (search exact)
      enterprise_type: type ?? undefined,
      delete_flag: false,
      // exclude partners that are pending or not normal user
      status: {
        not: USER_STATUS_ENUM.PENDING,
      },
      role: ROLES_ENUM.NORMAL_USER,
      // exclude current user and current user's partners
      id: {
        not: {
          in: [...partnerIds, user.id],
        },
      },
      enterprise: {
        delete_flag: false,
      },
    };

    // calculate total item
    const total_item = await connect.users.count({
      where: searchCondition,
    });

    // if total item is 0, return empty items
    if (!total_item) {
      return this.SUCCESS({
        total_item,
        items: [],
        page: 1,
        page_size: +rowsPerPage,
      });
    }

    const items = await connect.users.findMany({
      where: searchCondition,
      select: {
        id: true,
        name: true,
        user_code: true,
        license_number: true,
        enterprise_type: true,
        staff_type: true,
      },
      orderBy: {
        id: "asc",
      },
    });

    return this.SUCCESS({
      total_item,
      items,
      page: page,
      page_size: +rowsPerPage,
    });
  }

  /**
   * get partner detail for hybrid search
   * @param {*} user user login information
   * @param {*} params query parameters
   * @returns partner detail
   */
  async getPartnerHybridDetail(user, params) {
    const connect = this.DB.READ;
    const { id } = params;
    const data = await connect.users.findFirst({
      where: {
        id: +id,
        delete_flag: false,
        // exclude partners that are not active or not normal user
        status: {
          not: USER_STATUS_ENUM.PENDING,
        },
        role: ROLES_ENUM.NORMAL_USER,
        enterprise: {
          delete_flag: false,
        }
      },
      select: {
        id: true,
        name: true,
        name_kana: true,
        enterprise_type: true,
        enterprise_id: true,
        staff_type: true,
        provinces: {
          select: {
            id: true,
            name: true,
          },
        },
        user_code: true,
        license_number: true,
        role: true,
        enterprise: {
          select: {
            enterprise_code: true
          }
        }
      },
    });
    if (!data) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }

    // get user apply information
    const userApply = await connect.users.findFirst({
      where: {
        enterprise_id: data.enterprise_id,
        delete_flag: false,
        status: {
          not: USER_STATUS_ENUM.PENDING,
        },
        // user apply mean user who has staff_type = ENTERPRISE
        staff_type: STAFF_TYPE_ENUM.ENTERPRISE,
      },
      select: {
        id: true,
        name: true,
      },
    });

    return this.SUCCESS({
      ...data,
      user_apply: userApply,
      enterprise_code: data.enterprise.enterprise_code,
    });
  }

  /**
   * register partner for hybrid search
   * @param {*} user user login information
   * @param {*} param query parameters { partner_id, partner_type }
   * @returns success message
   */
  async registerPartnerHybrid(user, param) {
    const connect = this.DB.WRITE;
    const { partner_id, partner_type } = param;

    // TODO: check logic for partner_type here

    // check if partner exists
    const partner = await connect.users.findFirst({
      where: {
        id: +partner_id,
        delete_flag: false,
      },
    });

    if (!partner) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }

    // check if partner is not the same enterprise as user and user is catching enterprise and partner is catching enterprise
    if (
      user.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
      partner.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
      user.enterprise_id !== partner.enterprise_id
    ) {
      throw new ControlledException(
        MESSAGE.MSG_DIFFERENT_NOTIFICATIONUMBER_PARTNER_ERROR,
      );
    }

    // check if user already has this partner
    const existingPartner = await connect.partners.findFirst({
      where: {
        user_id: user.id,
        partner_id: +partner_id,
        delete_flag: false,
      },
    });

    if (existingPartner) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }

    // create new partner
    await connect.partners.upsert({
      where: {
        user_id_partner_id: {
          user_id: user.id,
          partner_id: +partner_id,
        },
      },
      update: {
        partner_type,
        hidden_flag: false,
        latest_updated_on: dayjs().toDate(),
        delete_flag: false,
        latest_updated_by_id: user.id,
      },
      create: {
        user_id: user.id,
        partner_id: +partner_id,
        hidden_flag: false,
        partner_type,
        delete_flag: false,
        created_by_id: user.id,
        created_on: dayjs().toDate(),
      },
    });

    return this.SUCCESS({ message: MESSAGE.MSG_RESISTER_CUSTOMER_INFO });
  }

  /**
   * register handmade product
   * @param {*} user user login information
   * @param {*} body request body
   * @returns success message
   */
  async registerHandMade(user, body) {
    const userId = user.id;
    const connect = this.DB.READ;
    const connectWrite = this.DB.WRITE;

    // check role of apply code and enterprise type
    if (
      body.enterpriseType === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
      !body.enterpriseCode.startsWith("0")
    ) {
      throw new ControlledException(
        MESSAGE.MSG_INVARID_NOTIFICATIONUMBER_SAIHO_ERROR,
      );
    }
    if (
      body.enterpriseType === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE &&
      !body.enterpriseCode.startsWith("5")
    ) {
      throw new ControlledException(
        MESSAGE.MSG_INVARID_NOTIFICATIONUMBER_DISTRIBUTOR_ERROR,
      );
    }

    // TODO: check logic for partner_type here

    // check if enterprise already exists
    const checkExistEnterPrise = await connect.enterprises.findFirst({
      where: {
        enterprise_code: body.enterpriseCode,
        delete_flag: false,
      },
    });

    if (!checkExistEnterPrise) {
      await connectWrite.$transaction(
        async (tx) => {
          // create new enterprise and user (partner)
          const enterpriseNew = await tx.enterprises.create({
            data: {
              enterprise_code: body.enterpriseCode,
              enterprise_name: body.enterpriseName,
              enterprise_name_kana: body.enterpriseNameKana || null,
              enterprise_name_nospace: body.enterpriseName.replace(/\s+/g, ""),
              enterprise_name_kana_nospace:
                body.enterpriseNameKana?.replace(/\s+/g, "") || null,
              type: body.enterpriseType,
              delete_flag: false,
              created_by_id: userId,
              created_on: dayjs().toDate(),
            },
          });
          const dateTimeNow = dayjs().toDate();
          const newUser = await tx.users.create({
            data: {
              enterprise_id: enterpriseNew.id,
              name: body.enterpriseName,
              name_kana: body.enterpriseNameKana || null,
              name_nospace: body.enterpriseName?.replace(/\s+/g, ""),
              name_kana_nospace:
                body.enterpriseNameKana?.replace(/\s+/g, "") || null,
              password: this.#generateSecurePassword(),
              phone: "",
              role: ROLES_ENUM.NORMAL_USER,
              enterprise_type: body.enterpriseType,
              staff_type: STAFF_TYPE_ENUM.ENTERPRISE,
              statistics_date_from:
                body.enterpriseType ===
                  ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE
                  ? dateTimeNow
                  : null,
              province_id: body.province,
              status: USER_STATUS_ENUM.NONACTIVE,
              latest_updated_on: dateTimeNow,
              created_on: dateTimeNow,
              created_by_id: userId,
            },
          });

          // create setting for new user
          await tx.settings.create({
            data: {
              user_id: newUser.id,
              price_per_kilogram: [200],
              delete_flag: false,
              qr_scan_init: SHOW_DEFAULT_SCAN_QR_ENUM.USE_CAMERA,
              price_per_quantity: [400],
              display_actual_received: true,
              display_shipment_weight: true,
              latest_updated_on: dateTimeNow,
              created_on: dateTimeNow,
              created_by_id: userId,
            },
          });

          const licenseCode = new ShortUniqueId({
            length: 16,
            dictionary: "alphanum",
          }).randomUUID();
          // create license for new user
          // if enterprise type is catching enterprise, set expiry date to today
          // otherwise set expiry date to null
          const newLicense = await tx.licenses.create({
            data: {
              expiry_date:
                body.enterpriseType === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE
                  ? dayjs.getDateFromJST().toDate()
                  : null,
              user_id: newUser.id,
              license_code: licenseCode,
              created_on: dateTimeNow,
              created_by_id: userId,
            },
          });
          await tx.users.update({
            where: {
              id: newUser.id,
            },
            data: {
              license_id: newLicense.id,
            },
          });

          // create partner for new user
          await tx.partners.create({
            data: {
              user_id: userId,
              partner_id: newUser.id,
              partner_type: body.partnerType,
              created_by_id: userId,
              created_on: dayjs().toDate(),
            },
          });
        },
        { isolationLevel: Prisma.TransactionIsolationLevel.Serializable },
      );
      return this.SUCCESS({ message: MESSAGE.MSG_RESISTER_CUSTOMER_INFO });
    } else {
      throw new ControlledException(MESSAGE.MSG_USED_NOTIFICATIONUMBER_ERROR);
    }
  }

  async toggleShow(params) {
    const connectRead = this.DB.READ;
    const connectWrite = this.DB.WRITE;
    const { userId, id } = params;
    const data = await connectRead.partners.findFirst({
      where: {
        user_id: userId,
        id: +id,
        delete_flag: false,
      },
    });
    if (!data) {
      throw new ControlledException(MESSAGE.MSG_NA_NOTIFICATIONUMBER_ERR);
    }
    await connectWrite.partners.update({
      where: {
        user_id: userId,
        id: +id,
        delete_flag: false,
      },
      data: {
        hidden_flag: !data.hidden_flag,
      },
    });
    return this.SUCCESS();
  }

  /**
   *
   * @param {*} user user info
   * @param {*} id partner id
   * @returns partner detail
   */
  async getLocatedTransactionDetail(user, id) {
    const connect = this.DB.READ;
    const data = await connect.partners.findFirst({
      where: {
        id: +id,
        user_id: user.id,
        delete_flag: false,
      },
      select: {
        id: true,
        hidden_flag: true,
        partner: {
          select: {
            user_code: true,
            provinces: true,
            staff_type: true,
            id: true,
            enterprise: true,
            name_nospace: true,
            name_kana_nospace: true,
            enterprise_type: true,
            license: true,
            name: true,
            name_kana: true,
            license_number: true,
            role: true,
          },
        },
        partner_type: true,
      },
    });
    if (!data) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }
    return this.SUCCESS({ ...data });
  }

  /**
   *
   * @param {*} user user info
   * @param {*} id partner id
   * @param {*} body { partnerType } number[]
   * @returns success or error
   */
  async updateLocatedTransaction(user, id, body) {
    const connect = this.DB.WRITE;
    const { partnerType } = body;

    // get partner
    const partner = await connect.partners.findFirst({
      where: {
        id: +id,
        user_id: user.id,
      },
      select: {
        partner: {
          select: {
            enterprise_type: true,
            staff_type: true,
          },
        },
        user: {
          select: {
            enterprise_type: true,
            staff_type: true,
          },
        },
      },
    });
    if (!partner) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }

    // check partner type is valid
    // this.#validatePartnerType(partnerType, partner.user, partner.partner);

    // update partner
    await connect.partners.update({
      where: {
        id: +id,
        user_id: user.id,
      },
      data: {
        partner_type: partnerType,
      },
    });
    return this.SUCCESS();
  }

  /**
   *
   * @param {*} user user info
   * @param {*} id partner id
   * @returns success or error
   */
  async deleteLocatedTransaction(user, id) {
    const connect = this.DB.WRITE;

    // get partner
    const partner = await connect.partners.findFirst({
      where: {
        id: +id,
        user_id: user.id,
      },
    });
    if (!partner) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }
    await connect.$transaction(async (tx) => {
      // delete partner
      await tx.partners.update({
        where: {
          id: +id,
          user_id: user.id,
        },
        data: {
          delete_flag: true,
        },
      });

      // check if user has destination_id in setting
      const settingUser = await tx.settings.findFirst({
        where: {
          user_id: user.id,
          delete_flag: false,
        },
      });
      if (settingUser?.destination_id) {
        // if user has destination_id in setting and destination_id is partner_id, set destination_id to null
        if (settingUser?.destination_id === partner.partner_id) {
          await tx.settings.update({
            where: {
              user_id: user.id,
              delete_flag: false,
            },
            data: {
              destination_id: null,
            },
          });
        }
      }
    });

    return this.SUCCESS();
  }
}

module.exports = LocatedTransaction;
