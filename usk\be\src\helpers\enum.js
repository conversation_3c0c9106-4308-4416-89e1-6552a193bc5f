const PAGINATION = {
  PAGE_SIZE: 10,
  FIRST_PAGE: 1,
  SORT_BY: 'date',
  DEFAULT_DESCENDING: true,
};

const SORT_BY_INVENTORY_ENUM = {
  CREATED_ON: 'created_on',
  ARRIVAL_DATE: 'latest_arrival_date',
  WEIGHT_INVENTORY: 'net_weight_inventory',
  NEW_WEIGHT_INVENTORY: 'new_net_weight_inventory',
  GROUP: 'group_name',
}

const MAIL_TEMPLATE = {
  ResetPassword: {
    file: '/emails/reset-password.handlebars',
    subject: 'Reset Password',
  },
};

const ROLES_ENUM = {
  SYSTEM_ADMIN: '99',
  ADMIN: '90',
  LOCAL_GOVERNMENT_ADMIN: '91',
  FISHERIES_DEPARTMENT_ADMIN: '92',
  NORMAL_USER: '00'
};


const ENTERPRISE_TYPE_ENUM = {
  CATCH_ENTERPRISE: 0,
  DISTRIBUTE_ENTERPRISE: 5,
  EEL_FARMING_ENTERPRISE: 9,
  FARM: 98,
  FOREIGN: 99,
};

const STAFF_TYPE_ENUM = {
  ENTERPRISE: 0,
  STAFF: 1,
};

const INVENTORY_CONTROL_TYPE_ENUM = {
  // 事業者が集約して管理
  GROUP: 1,
  // 各従事者が個別管理
  USER: 2,
}

// TODO: Remove this after refactoring
const UNIT_TYPE_SETTING_ENUM = {
  WEIGHT_ONLY: 1,
  QUANTITY_ONLY: 2,
};

const VOLUME_TYPE_ENUM = {
  WEIGHT: 1,
  QUANTITY: 2,
};

const SHOW_SHIPPING_DESTINATION_ENUM = {
  SHOW: true,
  HIDDEN: false,
};

const SHOW_DEFAULT_SCAN_QR_ENUM = {
  USE_CAMERA: 1,
  USE_SCAN: 2,
  USE_USER_ID: 3,
};

const TYPE_DIFFERENCE_WEIGHT_ENUM = {
  DEATH: 1,
  WEIGHT_ERROR: 2,
  OTHER: 3,
  BY_SYSTEM: 10,
};

const PARTNER_TYPE_ENUM = {
  SUPPLIER: 1,
  SHIPPER: 2,
};

const NOTIFICATION_STATUS_ENUM = {
  ALL: 'all',
  READ: 'read',
  UNREAD: 'unread',
}

const EXPORT_ENUM = {
  SHOW: true,
  HIDDEN: false,
};

const LIMIT_EXPORT = 10000;

const SHIPPING_TYPE_ENUM = {
  ARRIVAL_MANUAL: 0,
  PROXY: 1,
  NORMAL: 2,
};

const ADMIN_SYSTEM_SETTING_KEYS_ENUM = {
  WEIGHT_ALERT_THRESHOLD: 'weight_alert_threshold',
  EDIT_DEADLINE_FOR_ARRIVAL: 'edit_deadline_for_arrival_shipping',
  CATCHING_RESERVE_PERIOD: 'catching_reserve_period',
};

const USER_STATUS_ENUM = {
  ACTIVE: 0,
  NONACTIVE: 1,
  PENDING: 2,
};

const REASON_RELATION_TYPE_ENUM = {
  OUTBOUND_SHIPMENT: 1,
};

const EXTENT_EXPIRED_DATE = 14;

const ALLOC_STATUS = {
  DRAFT: 1,
  COMPLETED: 2,
}

const INVENTORY_TYPE_ENUM = {
  DOMESTIC: 0,
  IMPORTED_EXPORTED: 1,
  OTHER: 2,
}

const INVENTORY_HISTORY_TYPE_ENUM = {
  UPDATE: 1,
  DELETE: 2,
}

const SHIPPING_INVENTORY_TYPE_ENUM = {
  DOMESTIC: 0,
  IMPORTED_EXPORTED: 1,
  OTHER: 2,
}

module.exports = {
  PAGINATION,
  MAIL_TEMPLATE,
  ROLES_ENUM,
  ENTERPRISE_TYPE_ENUM,
  UNIT_TYPE_SETTING_ENUM,
  SHOW_SHIPPING_DESTINATION_ENUM,
  SHOW_DEFAULT_SCAN_QR_ENUM,
  TYPE_DIFFERENCE_WEIGHT_ENUM,
  SORT_BY_INVENTORY_ENUM,
  PARTNER_TYPE_ENUM,
  EXPORT_ENUM,
  LIMIT_EXPORT,
  SHIPPING_TYPE_ENUM,
  NOTIFICATION_STATUS_ENUM,
  ADMIN_SYSTEM_SETTING_KEYS_ENUM,
  USER_STATUS_ENUM,
  EXTENT_EXPIRED_DATE,
  REASON_RELATION_TYPE_ENUM,
  STAFF_TYPE_ENUM,
  ALLOC_STATUS,
  INVENTORY_CONTROL_TYPE_ENUM,
  INVENTORY_TYPE_ENUM,
  VOLUME_TYPE_ENUM,
  SHIPPING_INVENTORY_TYPE_ENUM,
  INVENTORY_HISTORY_TYPE_ENUM,
};
