<template>
  <q-dialog v-model="provideData.isPopup.value" persistent>
    <q-card class="tw:min-w-[80%] ">
      <div class="tw:flex tw:flex-col">
        <div class="
         tw:font-[700] tw:text-m-design tw:bg-[#004AB9] tw:text-white tw:p-5">
          {{ provideData.titlePopup }}</div>
        <div class="tw:text-m-design tw:p-5">
          {{ provideData.caption.value ?? provideData.caption }}
        </div>
        <div class="tw:flex tw:justify-center tw:items-center tw:tl:justify-end tw:p-5 tw:pt-0 tw:tl:flex-row tw:flex-col tw:gap-4">
          <BaseButton class="tw:tl:w-[20rem] tw:w-[70%] tw:flex tw:justify-center tw:items-center tw:rounded-full
              tw:font-[700] tw:text-xs-design tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
              " :class="`tw:text-[#004AB9]`" outline label="いいえ" @click.prevent="closeModal" />
          <BaseButton type="button" class="tw:flex tw:justify-center tw:items-center tw:text-white tw:font-[700]
              tw:tl:w-[20rem] tw:w-[70%] tw:rounded-full tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
              tw:text-xs-design" :class="`tw:bg-[#004AB9]`" @click.prevent="acceptModal" label="はい" />
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { inject } from 'vue';

import BaseButton from './base/vs/BaseButton.vue';

const { colorMain, colorSub } = storeToRefs(useAppStore());
const provideData = inject('popupConfirmTextProvideData');

const closeModal = () => {
  provideData.handleCloseModal();
};

const acceptModal = () => {
  provideData.handleAcceptModal();
};

</script>
