import MESSAGE from 'src/helpers/message';

export const registerArrivalStep1 = {
  type: 'object',
  additionalProperties: false,
  required: ['date'],
  properties: {
    date: {
      type: 'string',
      format: 'slash-date',
      minLength: 1,
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
  },
};

export const registerArrivalStep2 = {
  type: 'object',
  additionalProperties: false,
  required: ['grossWeight', 'tareWeight'],
  properties: {
    grossWeight: {
      type: 'number',
      exclusiveMinimum: 0,
      maximum: 99999999,
      errorMessage: {
        exclusiveMinimum: MESSAGE.MSG_LIMITS_GROSSWEIGHT_MIN_ERROR,
        maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    tareWeight: {
      type: 'number',
      minimum: 0,
      exclusiveMaximum: {
        $data: '1/grossWeight',
      },
      maximum: 99999999,
      errorMessage: {
        minimum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MIN_ERROR,
        exclusiveMaximum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MAX_ERROR,
        maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
  },
};
