<template>
  <div class="parentPdf tw:font-pdfSans">
    <div id="large-body-pdf">
      <div :style="{ textAlign: 'center', fontSize: '30px' }">
        入荷実績
      </div>
      <div>
        <div
          :style="{ paddingTop: '4px', paddingBottom: '4px', fontSize: '14px' }"
        >
          作成日：
          <span>{{ FORMAT_DATE_JAPAN() }}</span>
        </div>
        <div
          :style="{ paddingTop: '4px', paddingBottom: '4px', fontSize: '14px' }"
        >
          作成者：{{ profile?.enterprise?.enterprise_name }}
        </div>
        <div
          :style="{ paddingTop: '4px', paddingBottom: '4px', fontSize: '14px' }"
        >
          実績表示期間：
          <span :style="{ fontSize: '14px' }">{{
            dataSearchExport?.startArrivalDate
              ? FORMAT_DATE_JAPAN(dataSearchExport?.startArrivalDate)
              : ''
          }}</span>
          <span
            :style="{ fontSize: '14px' }"
            v-if="
              dataSearchExport?.startArrivalDate ||
              dataSearchExport?.endArrivalDate
            "
            >～</span
          >
          <span :style="{ fontSize: '14px' }">{{
            dataSearchExport?.endArrivalDate
              ? FORMAT_DATE_JAPAN(dataSearchExport?.endArrivalDate)
              : ''
          }}</span>
        </div>
        <div
          :style="{ paddingTop: '4px', paddingBottom: '4px', fontSize: '14px' }"
        >
          検索条件：
          <span :style="{ fontSize: '14px' }">
            {{ dataSearchExport.licenseNumber || '' }}</span
          >
          <span
            :style="{ fontSize: '14px' }"
            v-if="
              dataSearchExport.licenseNumber &&
              (dataSearchExport?.note1 || dataSearchExport?.note2)
            "
            >、</span
          >
          <!-- 備考1 note1 -->
          <span :style="{ fontSize: '14px' }">
            {{ dataSearchExport?.note1 || '' }}</span
          >
          <span
            :style="{ fontSize: '14px' }"
            v-if="dataSearchExport?.note1 && dataSearchExport?.note2"
            >、</span
          >
          <!-- 備考2 note2 -->
          <span :style="{ fontSize: '14px' }">
            {{ dataSearchExport?.note2 || '' }}</span
          >
        </div>
      </div>
      <div class="tw:flex tw:w-full tw:mt-7" :style="{ pageBreakInside: 'avoid' }">
        <div class="row-item" :style="{ textAlign: 'center', width: '90px' }">
          入荷日
        </div>
        <div
          class="row-item"
          :style="{ textAlign: 'center', width: '190px' }"
        >
          仕入先※事業者
        </div>
        <div
          class="row-item"
          :style="{ textAlign: 'center', width: '160px' }"
        >
          許可番号
        </div>
        <div
          class="row-item"
          :style="{ textAlign: 'center', width: '190px' }"
        >
          漁獲番号/荷口番号
        </div>
        <div
          class="row-item"
          :style="{ textAlign: 'center', width: '190px' }"
        >
          入荷者(届出事業者)
        </div>
        <div
          class="row-item"
          :style="{ textAlign: 'center', width: '100px' }"
        >
          入荷量[g]
        </div>
        <div
          class="row-item"
          :style="{ textAlign: 'center', width: '190px' }"
        >
          差異の理由
        </div>
      </div>

      <div
        v-for="(item, index) in dataAll"
        :key="index"
        class="tw:flex tw:w-full"
        :style="{ pageBreakInside: 'avoid' }"
      >
        <div class="row-item" :style="{ textAlign: 'center', width: '90px' }">
          {{ FORMAT_DATE(item.arrival_date) }}
        </div>
        <div class="row-item" :style="{ textAlign: 'left', width: '190px' }">
          {{ `${item.destination_enterprise_name}${item.mark_staff}` }}
        </div>
        <div class="row-item" :style="{ textAlign: 'left', width: '160px' }">
          {{ item.license_number }}
        </div>
        <div class="row-item" :style="{ textAlign: 'left', width: '190px' }">
          {{ item.code }}
        </div>
        <div class="row-item" :style="{ textAlign: 'left', width: '190px' }">
          {{ `${item.destination_user_name}`}}
        </div>
        <div class="row-item" :style="{ textAlign: 'right', width: '100px' }">
          {{ FORMAT_NUMBER(item.arrival_net_weight) }}
        </div>
        <div class="row-item" :style="{ textAlign: 'left', width: '190px' }">
          {{ item.reason_diff }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { inject, onMounted, ref } from 'vue';
import { FORMAT_DATE, FORMAT_NUMBER, FORMAT_DATE_JAPAN } from 'helpers/common';
import profileService from 'services/profile.service';

const dataAll = inject('arrivalListDataExportPdf');
const dataSearchExport = inject('dataSearchExport');
const profile = ref({});

onMounted(async () => {
  const result = await profileService.getProfile();
  profile.value = result.payload.data;
});
</script>

<style scoped>
.parentPdf {
  font-size: 100% !important;
  visibility: hidden;
  position: absolute;
}

table {
  margin-top: 32px;
  width: 100%;
  border-collapse: collapse;
}

th,
td,
.row-item {
  border: 1px solid #000;
  padding-left: 8px !important;
  padding-right: 8px !important;
  padding-top: 4px !important;
  padding-bottom: 16px !important;
  font-weight: 400;
  vertical-align: middle;
  font-size: 12px !important;
}

tr {
  page-break-inside: avoid;
}

th {
  background-color: #fff;
  text-align: 'center';
}
</style>
